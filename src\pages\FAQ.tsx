
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from '@/components/ui/accordion';
import { Heart, HelpCircle, Code, Building2, Phone, Mail } from 'lucide-react';
import { useSEO } from '@/utils/seo';
import { FAQStructuredData, BreadcrumbStructuredData } from '@/components/StructuredData';

const FAQ = () => {
  // SEO optimization for FAQ page
  useSEO('faq', {
    title: "Frequently Asked Questions | PledgeForLove",
    description: "Get answers to common questions about PledgeForLove 's digital wedding contribution platform. Learn how to create pledge cards, manage contributions, and more.",
    keywords: "FAQ, help, wedding contributions, pledge cards, wedding platform, digital wedding, questions"
  });

  const faqData = [
    {
      question: "How does PledgeForLove work?",
      answer: "PledgeForLove allows couples to create digital pledge cards for their wedding. Guests can view these cards, make pledges, and contribute to the couple's wedding fund. The platform tracks all contributions and provides real-time updates to the couple."
    },
    {
      question: "How do guests make contributions?",
      answer: "Guests can contribute through various methods including mobile money (MTN Mobile Money, Airtel Money), bank transfers, or cash payments. Reach out to the Treasurer for specific instructions."
    },
    {
      question: "Can I track who has contributed?",
      answer: "Yes, the platform provides a comprehensive dashboard where you can see all contributions, track payment status, and manage guest pledges in real-time."
    },
    {
      question: "Is my data secure on PledgeForLove?",
      answer: "Security is our top priority. We use industry-standard encryption and security measures to protect your personal information and financial data. Your privacy is always maintained."
    },
    {
      question: "Can I customize my pledge cards?",
      answer: "Yes, you can personalize your pledge cards with your wedding details, photos, and custom messages. The platform offers various templates and customization options."
    },
    {
      question: "What payment methods are supported?",
      answer: "We are working on integrating direct payment opptions to make it easier for family and friends to contribute. For now, all pledges are sent to the Treasurer whose details are always availed at the bottom of the card."
    },
    {
      question: "Can I share my pledge cards on social media?",
      answer: "Absolutely! The platform includes built-in social sharing features for Facebook, Twitter, WhatsApp, and email. You can easily share your pledge cards with friends and family."
    },
    {
      question: "Do I need technical skills to use PledgeForLove?",
      answer: "Not at all! PledgeForLove is designed to be user-friendly and intuitive. The platform guides you through each step, making it easy for anyone to create and manage their wedding contributions."
    },
    {
      question: "Can I use PledgeForLove on my mobile phone?",
      answer: "Yes, PledgeForLove is fully mobile-responsive and works perfectly on smartphones and tablets. You can manage your wedding contributions from anywhere."
    },
    {
      question: "What happens after my wedding?",
      answer: "Your account and data remain accessible after your wedding. You can continue to track any pending contributions and download reports of all your wedding contributions for your records."
    }
  ];

  // Prepare structured data for FAQ
  const faqStructuredData = {
    questions: faqData.map(item => ({
      "@type": "Question",
      name: item.question,
      acceptedAnswer: {
        "@type": "Answer",
        text: item.answer
      }
    }))
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-pink-50 to-purple-50 py-12">
      {/* Structured Data */}
      <FAQStructuredData data={faqStructuredData} />
      <BreadcrumbStructuredData
        items={[
          { name: "Home", url: "https://p4love.com" },
          { name: "FAQ", url: "https://p4love.com/faq" }
        ]}
      />

      <div className="max-w-4xl mx-auto px-6">
        {/* Header */}
        <div className="text-center mb-12">
          <div className="flex justify-center mb-6">
            <div className="bg-pink-100 p-4 rounded-full">
              <HelpCircle className="h-12 w-12 text-pink-600" />
            </div>
          </div>
          <h1 className="text-4xl font-bold text-rose-800 mb-4">
            Frequently Asked Questions
          </h1>
          <p className="text-lg text-rose-700/90 max-w-2xl mx-auto">
            Find answers to common questions about PledgeForLove 's digital wedding contribution platform.
          </p>
        </div>

        {/* FAQ Content */}
        <Card className="bg-white/80 backdrop-blur-sm border-pink-100">
          <CardHeader>
            <CardTitle className="flex items-center gap-2 text-rose-800">
              <Heart className="h-5 w-5 text-pink-500" />
              Common Questions
            </CardTitle>
            <CardDescription>
              Everything you need to know about using PledgeForLove for your wedding
            </CardDescription>
          </CardHeader>
          <CardContent>
            <Accordion type="single" collapsible className="space-y-2">
              {faqData.map((item, index) => (
                <AccordionItem 
                  key={index} 
                  value={`item-${index}`}
                  className="border border-pink-100 rounded-lg px-4"
                >
                  <AccordionTrigger className="text-left text-rose-800 hover:text-pink-600">
                    {item.question}
                  </AccordionTrigger>
                  <AccordionContent className="text-rose-700/90 pt-2">
                    {item.answer}
                  </AccordionContent>
                </AccordionItem>
              ))}
            </Accordion>
          </CardContent>
        </Card>

        {/* Developer Information Section */}
        <div className="mt-12">
          <Card className="bg-gradient-to-r from-blue-50 to-indigo-50 border-blue-200">
            <CardHeader>
              <CardTitle className="flex items-center gap-2 text-blue-800">
                <Code className="h-5 w-5 text-blue-600" />
                About the Developer
              </CardTitle>
              <CardDescription>
                Learn about the company behind PledgeForLove
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="flex items-start gap-4">
                <div className="bg-blue-100 p-3 rounded-full">
                  <Building2 className="h-6 w-6 text-blue-600" />
                </div>
                <div className="flex-1">
                  <h3 className="text-lg font-semibold text-blue-800 mb-2">Tom's Cyber Lab</h3>
                  <p className="text-blue-700/90 mb-4">
                    Tom's Cyber Lab is a business platform that provides expertise in ICT solutions for businesses,
                    including digital platforms, device procurement, website development, digital marketing,
                    computer repair & maintenance, and computer network security techniques to improve our
                    customers' positioning on online presence world wide. We offer services to both individuals,
                    organisations, and institutions.
                  </p>
                  <div className="space-y-2 text-sm">
                    <p className="text-blue-700">
                      <strong>Registration No.:</strong> 80020002602390
                    </p>
                  </div>
                </div>
              </div>

              <div className="border-t border-blue-200 pt-6">
                <h4 className="text-md font-semibold text-blue-800 mb-4">Contact Tom's Cyber Lab</h4>
                <div className="grid sm:grid-cols-2 gap-4">
                  <div className="flex items-center gap-3">
                    <Phone className="h-5 w-5 text-blue-600" />
                    <div>
                      <p className="text-sm font-medium text-blue-800">WhatsApp</p>
                      <a
                        href="https://wa.me/256777959328"
                        className="text-blue-600 hover:text-blue-800 transition-colors"
                        target="_blank"
                        rel="noopener noreferrer"
                      >
                        +256777959328
                      </a>
                    </div>
                  </div>
                  <div className="flex items-center gap-3">
                    <Mail className="h-5 w-5 text-blue-600" />
                    <div>
                      <p className="text-sm font-medium text-blue-800">Email</p>
                      <a
                        href="mailto:<EMAIL>"
                        className="text-blue-600 hover:text-blue-800 transition-colors"
                      >
                        <EMAIL>
                      </a>
                    </div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
};

export default FAQ; // eslint-disable-line react-refresh/only-export-components
