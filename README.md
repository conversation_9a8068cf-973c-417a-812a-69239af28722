# Love Pledge Uganda

## Project Overview

Love Pledge Uganda is a modern web application that allows couples to create digital pledge cards for their weddings. Guests can view these cards, make pledges, and contribute to the couple's wedding fund. The platform provides real-time tracking and management of all contributions.

## Development Setup

To work locally using your preferred IDE, you can clone this repo and push changes.

The only requirement is having Node.js & npm installed - [install with nvm](https://github.com/nvm-sh/nvm#installing-and-updating)

Follow these steps:

```sh
# Step 1: Clone the repository using the project's Git URL.
git clone <YOUR_GIT_URL>

# Step 2: Navigate to the project directory.
cd <YOUR_PROJECT_NAME>

# Step 3: Install the necessary dependencies.
npm i

# Step 4: Start the development server with auto-reloading and an instant preview.
npm run dev
```

**Edit a file directly in GitHub**

- Navigate to the desired file(s).
- Click the "Edit" button (pencil icon) at the top right of the file view.
- Make your changes and commit the changes.

**Use GitHub Codespaces**

- Navigate to the main page of your repository.
- Click on the "Code" button (green button) near the top right.
- Select the "Codespaces" tab.
- Click on "New codespace" to launch a new Codespace environment.
- Edit files directly within the Codespace and commit and push your changes once you're done.

## What technologies are used for this project?

This project is built with:

- Vite
- TypeScript
- React
- shadcn-ui
- Tailwind CSS

## How can I deploy this project?

This project can be deployed to any static hosting service such as:

- **Vercel**: `npm i -g vercel && vercel --prod`
- **Netlify**: `npm i -g netlify-cli && netlify deploy --prod --dir=dist`
- **GitHub Pages**: Configure GitHub Actions for automatic deployment
- **Any static hosting**: Upload the `dist/` folder after running `npm run build`

## Environment Variables

Make sure to set up the following environment variables for production:

```env
VITE_SUPABASE_URL=your_supabase_url
VITE_SUPABASE_ANON_KEY=your_supabase_anon_key
```
