import React, { useState, useEffect, useCallback } from 'react';
import { useNavigate } from 'react-router-dom';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { 
  DollarSign, 
  Plus, 
  Settings, 
  Download, 
  ArrowLeft,
  Calculator,
  TrendingUp
} from 'lucide-react';
import { useAuth } from '@/contexts/AuthContext';
import { useToast } from '@/hooks/use-toast';
import { useSEO } from '@/utils/seo';
import LoadingSpinner from '@/components/ui/loading-spinner';
import BudgetDashboard from '@/components/budget/BudgetDashboard';
import BudgetSetupWizard from '@/components/budget/BudgetSetupWizard';
import BudgetItemManager from '@/components/budget/BudgetItemManager';
import { budgetOperations } from '@/integrations/budget';
import { exportBudgetToExcel } from '@/utils/budgetUtils';
import type { Budget, BudgetItem, BudgetSummary, CeremonyType } from '@/types/app';

const Budget = () => {
  const { user, profile } = useAuth();
  const navigate = useNavigate();
  const { toast } = useToast();
  const [loading, setLoading] = useState(true);
  const [budget, setBudget] = useState<Budget | null>(null);
  const [items, setItems] = useState<BudgetItem[]>([]);
  const [summary, setSummary] = useState<BudgetSummary | null>(null);
  const [showSetupWizard, setShowSetupWizard] = useState(false);
  const [activeTab, setActiveTab] = useState('overview');

  // SEO optimization
  useSEO({
    title: "Budget Management | PledgeForLove",
    description: "Manage your wedding budget, track expenses, and stay organized with our comprehensive budgeting tools.",
    keywords: "wedding budget, expense tracking, budget management, wedding planning"
  });

  useEffect(() => {
    if (user) {
      loadBudgetData();
    }
  }, [user]);

  const loadBudgetData = useCallback(async () => {
    if (!user) return;
    
    try {
      setLoading(true);
      const data = await budgetOperations.getCompleteBudgetData(user.id);
      setBudget(data.budget);
      setItems(data.items);
      setSummary(data.summary);
    } catch (error) {
      console.error('Error loading budget data:', error);
      toast({
        title: "Error",
        description: "Failed to load budget data. Please try again.",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  }, [user, toast]);

  const handleCreateBudget = () => {
    setShowSetupWizard(true);
  };

  const handleSetupComplete = () => {
    setShowSetupWizard(false);
    loadBudgetData();
    setActiveTab('overview');
  };

  const handleExportBudget = async () => {
    if (!budget || !items.length) {
      toast({
        title: "No Data to Export",
        description: "Create a budget and add items before exporting.",
        variant: "destructive",
      });
      return;
    }

    try {
      await exportBudgetToExcel(budget, items, summary);
      toast({
        title: "Export Successful! 📊",
        description: "Your budget has been exported to Excel.",
      });
    } catch (error) {
      console.error('Error exporting budget:', error);
      toast({
        title: "Export Failed",
        description: "Failed to export budget. Please try again.",
        variant: "destructive",
      });
    }
  };

  if (!user) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <LoadingSpinner text="Loading..." />
      </div>
    );
  }

  if (loading) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="space-y-6">
          <div className="h-8 bg-gray-200 rounded w-1/3 animate-pulse"></div>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            {[1, 2, 3].map((i) => (
              <Card key={i} className="animate-pulse">
                <CardHeader className="pb-3">
                  <div className="h-4 bg-gray-200 rounded w-1/2"></div>
                </CardHeader>
                <CardContent>
                  <div className="h-8 bg-gray-200 rounded w-3/4"></div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </div>
    );
  }

  const ceremonyType = (profile?.ceremony_type as CeremonyType) || 'wedding';

  return (
    <div className="container mx-auto px-4 py-8">
      {/* Header */}
      <div className="mb-8">
        <div className="flex items-center gap-4 mb-4">
          <Button 
            variant="ghost" 
            size="sm" 
            onClick={() => navigate('/dashboard')}
            className="flex items-center gap-2"
          >
            <ArrowLeft className="h-4 w-4" />
            Back to Dashboard
          </Button>
        </div>
        
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-gray-900 flex items-center gap-3">
              <Calculator className="h-8 w-8 text-primary" />
              Budget Management
            </h1>
            <p className="text-gray-600 mt-2">
              Track and manage your {ceremonyType} expenses
            </p>
          </div>
          
          {budget && (
            <div className="flex gap-2">
              <Button variant="outline" onClick={handleExportBudget}>
                <Download className="h-4 w-4 mr-2" />
                Export
              </Button>
              <Button onClick={handleCreateBudget}>
                <Plus className="h-4 w-4 mr-2" />
                New Budget
              </Button>
            </div>
          )}
        </div>
      </div>

      {/* Setup Wizard Dialog */}
      <Dialog open={showSetupWizard} onOpenChange={setShowSetupWizard}>
        <DialogContent className="max-w-5xl max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>Budget Setup</DialogTitle>
            <DialogDescription>
              Create a new budget for your {ceremonyType}
            </DialogDescription>
          </DialogHeader>
          <BudgetSetupWizard
            userId={user.id}
            initialCeremonyType={ceremonyType}
            onComplete={handleSetupComplete}
            onCancel={() => setShowSetupWizard(false)}
          />
        </DialogContent>
      </Dialog>

      {/* Main Content */}
      {!budget ? (
        <Card>
          <CardHeader className="text-center">
            <CardTitle className="flex items-center justify-center gap-2">
              <DollarSign className="h-6 w-6" />
              Welcome to Budget Management
            </CardTitle>
            <CardDescription>
              Create your first budget to start tracking expenses for your {ceremonyType}
            </CardDescription>
          </CardHeader>
          <CardContent className="text-center space-y-4">
            <div className="bg-blue-50 border border-blue-200 rounded-lg p-6">
              <TrendingUp className="h-12 w-12 text-blue-600 mx-auto mb-4" />
              <h3 className="font-semibold text-blue-900 mb-2">Smart Budget Management</h3>
              <p className="text-blue-800 text-sm mb-4">
                Get ceremony-specific budget categories, track expenses in real-time, 
                and export your data to Excel for easy planning.
              </p>
              <ul className="text-left text-sm text-blue-800 space-y-1">
                <li>• Ceremony-specific budget categories</li>
                <li>• Real-time expense tracking</li>
                <li>• Progress monitoring and alerts</li>
                <li>• Excel export functionality</li>
                <li>• Cultural guidance and tips</li>
              </ul>
            </div>
            <Button onClick={handleCreateBudget} size="lg" className="w-full sm:w-auto">
              <Plus className="h-5 w-5 mr-2" />
              Create Your First Budget
            </Button>
          </CardContent>
        </Card>
      ) : (
        <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
          <TabsList className="grid w-full grid-cols-2">
            <TabsTrigger value="overview">Budget Overview</TabsTrigger>
            <TabsTrigger value="manage">Manage Items</TabsTrigger>
          </TabsList>

          <TabsContent value="overview" className="space-y-6">
            <BudgetDashboard
              userId={user.id}
              ceremonyType={ceremonyType}
              onCreateBudget={handleCreateBudget}
              onManageItems={() => setActiveTab('manage')}
              onExportBudget={handleExportBudget}
            />
          </TabsContent>

          <TabsContent value="manage" className="space-y-6">
            <BudgetItemManager
              userId={user.id}
              budgetId={budget.id}
              ceremonyType={ceremonyType}
              currency={budget.currency}
              onItemsChange={loadBudgetData}
            />
          </TabsContent>
        </Tabs>
      )}
    </div>
  );
};

export default Budget;
