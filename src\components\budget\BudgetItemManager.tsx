import React, { useState, useEffect, useCallback } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Badge } from '@/components/ui/badge';
import { Switch } from '@/components/ui/switch';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { 
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { 
  Plus, 
  Edit, 
  Trash2, 
  CheckCircle, 
  Circle,
  DollarSign,
  TrendingUp,
  AlertCircle
} from 'lucide-react';
import { useToast } from '@/hooks/use-toast';
import { budgetItemService } from '@/integrations/budget';
import { formatCurrency } from '@/utils/budgetUtils';
import type { BudgetItem, CeremonyType } from '@/types/app';

interface BudgetItemManagerProps {
  userId: string;
  budgetId: string;
  ceremonyType?: CeremonyType;
  currency?: string;
  onItemsChange?: () => void;
}

interface ItemFormData {
  category: string;
  estimated_cost: number;
  actual_cost: number | null;
  notes: string;
  is_completed: boolean;
}

export const BudgetItemManager: React.FC<BudgetItemManagerProps> = ({
  userId,
  budgetId,
  ceremonyType: _ceremonyType = 'wedding', // Prefixed with underscore to indicate intentionally unused
  currency = 'UGX',
  onItemsChange
}) => {
  const [items, setItems] = useState<BudgetItem[]>([]);
  const [loading, setLoading] = useState(true);
  const [editingItem, setEditingItem] = useState<BudgetItem | null>(null);
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [formData, setFormData] = useState<ItemFormData>({
    category: '',
    estimated_cost: 0,
    actual_cost: null,
    notes: '',
    is_completed: false
  });
  const { toast } = useToast();

  useEffect(() => {
    loadItems();
  }, [budgetId]);

  const loadItems = useCallback(async () => {
    try {
      setLoading(true);
      const data = await budgetItemService.getBudgetItemsByBudgetId(budgetId);
      setItems(data);
    } catch (error) {
      console.error('Error loading budget items:', error);
      toast({
        title: "Error",
        description: "Failed to load budget items. Please try again.",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  }, [budgetId, toast]);

  const resetForm = () => {
    setFormData({
      category: '',
      estimated_cost: 0,
      actual_cost: null,
      notes: '',
      is_completed: false
    });
    setEditingItem(null);
  };

  const openDialog = (item?: BudgetItem) => {
    if (item) {
      setEditingItem(item);
      setFormData({
        category: item.category,
        estimated_cost: item.estimated_cost,
        actual_cost: item.actual_cost,
        notes: item.notes || '',
        is_completed: item.is_completed
      });
    } else {
      resetForm();
    }
    setIsDialogOpen(true);
  };

  const closeDialog = () => {
    setIsDialogOpen(false);
    resetForm();
  };

  const handleSave = async () => {
    try {
      if (editingItem) {
        // Update existing item
        await budgetItemService.updateBudgetItem(userId, editingItem.id, formData);
        toast({
          title: "Item Updated",
          description: "Budget item has been updated successfully.",
        });
      } else {
        // Create new item
        await budgetItemService.createBudgetItem(userId, {
          budget_id: budgetId,
          ...formData
        });
        toast({
          title: "Item Added",
          description: "New budget item has been added successfully.",
        });
      }
      
      await loadItems();
      onItemsChange?.();
      closeDialog();
    } catch (error) {
      console.error('Error saving budget item:', error);
      toast({
        title: "Error",
        description: "Failed to save budget item. Please try again.",
        variant: "destructive",
      });
    }
  };

  const handleDelete = async (itemId: string) => {
    if (!confirm('Are you sure you want to delete this budget item?')) {
      return;
    }

    try {
      await budgetItemService.deleteBudgetItem(userId, itemId);
      toast({
        title: "Item Deleted",
        description: "Budget item has been deleted successfully.",
      });
      await loadItems();
      onItemsChange?.();
    } catch (error) {
      console.error('Error deleting budget item:', error);
      toast({
        title: "Error",
        description: "Failed to delete budget item. Please try again.",
        variant: "destructive",
      });
    }
  };

  const toggleCompletion = async (item: BudgetItem) => {
    try {
      const isMarkingComplete = !item.is_completed;

      // When marking as complete, set actual_cost to estimated_cost if not already set
      // When marking as incomplete, keep the actual_cost as is (user might want to preserve it)
      const updates: { is_completed: boolean; actual_cost?: number } = {
        is_completed: isMarkingComplete
      };

      if (isMarkingComplete && (item.actual_cost === null || item.actual_cost === undefined || item.actual_cost === 0)) {
        updates.actual_cost = item.estimated_cost;
      }

      await budgetItemService.updateBudgetItem(userId, item.id, updates);
      await loadItems();
      onItemsChange?.();

      toast({
        title: "Success",
        description: isMarkingComplete
          ? "Item marked as complete and actual cost updated"
          : "Item marked as incomplete",
      });
    } catch (error) {
      console.error('Error updating item completion:', error);
      toast({
        title: "Error",
        description: "Failed to update item status. Please try again.",
        variant: "destructive",
      });
    }
  };

  const getTotalEstimated = () => items.reduce((sum, item) => sum + item.estimated_cost, 0);
  const getTotalActual = () => items.reduce((sum, item) => sum + (item.actual_cost || 0), 0);
  const getCompletedCount = () => items.filter(item => item.is_completed).length;

  if (loading) {
    return (
      <Card>
        <CardHeader>
          <div className="h-6 bg-gray-200 rounded w-1/3 animate-pulse"></div>
          <div className="h-4 bg-gray-200 rounded w-1/2 animate-pulse"></div>
        </CardHeader>
        <CardContent>
          <div className="space-y-3">
            {[1, 2, 3].map((i) => (
              <div key={i} className="h-12 bg-gray-200 rounded animate-pulse"></div>
            ))}
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      {/* Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-2">
              <DollarSign className="h-4 w-4 text-blue-600" />
              <div>
                <p className="text-sm text-muted-foreground">Estimated</p>
                <p className="font-medium">{formatCurrency(getTotalEstimated(), currency)}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-2">
              <TrendingUp className="h-4 w-4 text-green-600" />
              <div>
                <p className="text-sm text-muted-foreground">Actual</p>
                <p className="font-medium">{formatCurrency(getTotalActual(), currency)}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-2">
              <CheckCircle className="h-4 w-4 text-purple-600" />
              <div>
                <p className="text-sm text-muted-foreground">Completed</p>
                <p className="font-medium">{getCompletedCount()} / {items.length}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-2">
              <AlertCircle className="h-4 w-4 text-orange-600" />
              <div>
                <p className="text-sm text-muted-foreground">Variance</p>
                <p className="font-medium">
                  {formatCurrency(getTotalActual() - getTotalEstimated(), currency)}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Items Table */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle>Budget Items</CardTitle>
              <CardDescription>
                Manage your budget categories and track expenses
              </CardDescription>
            </div>
            <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
              <DialogTrigger asChild>
                <Button onClick={() => openDialog()}>
                  <Plus className="h-4 w-4 mr-2" />
                  Add Item
                </Button>
              </DialogTrigger>
              <DialogContent className="max-w-md">
                <DialogHeader>
                  <DialogTitle>
                    {editingItem ? 'Edit Budget Item' : 'Add Budget Item'}
                  </DialogTitle>
                  <DialogDescription>
                    {editingItem ? 'Update the budget item details' : 'Add a new category to your budget'}
                  </DialogDescription>
                </DialogHeader>
                <div className="space-y-4">
                  <div className="space-y-2">
                    <Label htmlFor="category">Category</Label>
                    <Input
                      id="category"
                      value={formData.category}
                      onChange={(e) => setFormData(prev => ({ ...prev, category: e.target.value }))}
                      placeholder="e.g., Venue & Catering"
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="estimated_cost">Estimated Cost</Label>
                    <Input
                      id="estimated_cost"
                      type="number"
                      value={formData.estimated_cost || ''}
                      onChange={(e) => setFormData(prev => ({ ...prev, estimated_cost: Number(e.target.value) }))}
                      placeholder="0"
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="actual_cost">Actual Cost (Optional)</Label>
                    <Input
                      id="actual_cost"
                      type="number"
                      value={formData.actual_cost || ''}
                      onChange={(e) => setFormData(prev => ({ 
                        ...prev, 
                        actual_cost: e.target.value ? Number(e.target.value) : null 
                      }))}
                      placeholder="0"
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="notes">Notes (Optional)</Label>
                    <Textarea
                      id="notes"
                      value={formData.notes}
                      onChange={(e) => setFormData(prev => ({ ...prev, notes: e.target.value }))}
                      placeholder="Additional notes..."
                      rows={3}
                    />
                  </div>

                  <div className="flex items-center space-x-2">
                    <Switch
                      id="is_completed"
                      checked={formData.is_completed}
                      onCheckedChange={(checked) => setFormData(prev => ({ ...prev, is_completed: checked }))}
                    />
                    <Label htmlFor="is_completed">Mark as completed</Label>
                  </div>

                  <div className="flex gap-2 pt-4">
                    <Button variant="outline" onClick={closeDialog} className="flex-1">
                      Cancel
                    </Button>
                    <Button onClick={handleSave} className="flex-1">
                      {editingItem ? 'Update' : 'Add'} Item
                    </Button>
                  </div>
                </div>
              </DialogContent>
            </Dialog>
          </div>
        </CardHeader>
        <CardContent>
          {items.length === 0 ? (
            <div className="text-center py-8">
              <p className="text-muted-foreground">No budget items yet</p>
              <p className="text-sm text-muted-foreground mt-1">
                Add your first budget category to get started
              </p>
            </div>
          ) : (
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Category</TableHead>
                  <TableHead>Estimated</TableHead>
                  <TableHead>Actual</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead>Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {items.map((item) => (
                  <TableRow key={item.id}>
                    <TableCell>
                      <div>
                        <p className="font-medium">{item.category}</p>
                        {item.notes && (
                          <p className="text-sm text-muted-foreground">{item.notes}</p>
                        )}
                      </div>
                    </TableCell>
                    <TableCell>{formatCurrency(item.estimated_cost, currency)}</TableCell>
                    <TableCell>
                      <div className="flex flex-col">
                        <span className={item.is_completed && item.actual_cost ? "text-green-600 font-medium" : ""}>
                          {item.actual_cost ? formatCurrency(item.actual_cost, currency) : '-'}
                        </span>
                        {item.is_completed && item.actual_cost === item.estimated_cost && (
                          <span className="text-xs text-muted-foreground">Auto-set</span>
                        )}
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center gap-2">
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => toggleCompletion(item)}
                          className="p-1"
                        >
                          {item.is_completed ? (
                            <CheckCircle className="h-4 w-4 text-green-600" />
                          ) : (
                            <Circle className="h-4 w-4 text-gray-400" />
                          )}
                        </Button>
                        <Badge variant={item.is_completed ? "default" : "secondary"}>
                          {item.is_completed ? 'Complete' : 'Pending'}
                        </Badge>
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="flex gap-1">
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => openDialog(item)}
                        >
                          <Edit className="h-4 w-4" />
                        </Button>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => handleDelete(item.id)}
                        >
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </div>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          )}
        </CardContent>
      </Card>
    </div>
  );
};

export default BudgetItemManager;
