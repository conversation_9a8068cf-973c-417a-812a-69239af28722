import type { Budget, BudgetItem, BudgetSummary } from '@/types/app';

// Budget validation functions
export const validateBudgetAmount = (amount: number): { isValid: boolean; errors: string[] } => {
  const errors: string[] = [];

  if (amount < 0) {
    errors.push('Budget amount cannot be negative');
  }

  if (amount > 999999999.99) {
    errors.push('Budget amount is too large');
  }

  return {
    isValid: errors.length === 0,
    errors
  };
};

export const validateBudgetItem = (item: Partial<BudgetItem>, isUpdate: boolean = false): { isValid: boolean; errors: string[] } => {
  const errors: string[] = [];

  // Only validate category as required for new items, not updates
  if (!isUpdate && !item.category?.trim()) {
    errors.push('Category is required');
  }

  // Validate category if it's being updated
  if (isUpdate && item.category !== undefined && !item.category?.trim()) {
    errors.push('Category cannot be empty');
  }

  if (item.estimated_cost !== undefined && item.estimated_cost < 0) {
    errors.push('Estimated cost cannot be negative');
  }

  if (item.actual_cost !== undefined && item.actual_cost < 0) {
    errors.push('Actual cost cannot be negative');
  }

  return {
    isValid: errors.length === 0,
    errors
  };
};

// Budget calculation functions
export const calculateBudgetProgress = (budget: Budget, items: BudgetItem[]): {
  totalEstimated: number;
  totalActual: number;
  totalSpent: number;
  remainingBudget: number;
  progressPercentage: number;
} => {
  const totalEstimated = items.reduce((sum, item) => sum + (item.estimated_cost || 0), 0);
  const totalActual = items.reduce((sum, item) => sum + (item.actual_cost || 0), 0);
  const totalSpent = items.reduce((sum, item) => sum + (item.actual_cost || 0), 0);
  const remainingBudget = budget.total_budget - totalSpent;
  const progressPercentage = budget.total_budget > 0 ? (totalSpent / budget.total_budget) * 100 : 0;

  return {
    totalEstimated,
    totalActual,
    totalSpent,
    remainingBudget,
    progressPercentage
  };
};

export const calculateBudgetSummary = (budget: Budget, items: BudgetItem[]): BudgetSummary => {
  const totalEstimated = items.reduce((sum, item) => sum + (item.estimated_cost || 0), 0);
  const totalActual = items.reduce((sum, item) => sum + (item.actual_cost || 0), 0);
  const totalSpent = items.reduce((sum, item) => sum + (item.actual_cost || 0), 0);
  const remainingBudget = budget.total_budget - totalSpent;

  return {
    total_budget: budget.total_budget,
    total_estimated: totalEstimated,
    total_actual: totalActual,
    total_spent: totalSpent,
    remaining_budget: remainingBudget
  };
};

// Budget formatting functions
export const formatCurrency = (amount: number, currency: string = 'UGX'): string => {
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: currency,
    minimumFractionDigits: 0,
    maximumFractionDigits: 0
  }).format(amount);
};

export const formatBudgetProgress = (spent: number, total: number): string => {
  if (total === 0) return '0%';
  const percentage = (spent / total) * 100;
  return `${percentage.toFixed(1)}%`;
};

// Budget category helpers
export const getDefaultBudgetCategories = (): string[] => [
  'Venue & Catering',
  'Photography & Videography',
  'Attire & Accessories',
  'Decoration & Flowers',
  'Music & Entertainment',
  'Transportation',
  'Accommodation',
  'Wedding Rings',
  'Stationery & Invitations',
  'Beauty & Grooming',
  'Wedding Cake',
  'Gifts & Favors',
  'Legal & Documentation',
  'Contingency',
  'Other'
];

export const getCategoryIcon = (category: string): string => {
  const categoryIcons: Record<string, string> = {
    'Venue & Catering': '🍽️',
    'Photography & Videography': '📸',
    'Attire & Accessories': '👗',
    'Decoration & Flowers': '🌸',
    'Music & Entertainment': '🎵',
    'Transportation': '🚗',
    'Accommodation': '🏨',
    'Wedding Rings': '💍',
    'Stationery & Invitations': '📝',
    'Beauty & Grooming': '💄',
    'Wedding Cake': '🎂',
    'Gifts & Favors': '🎁',
    'Legal & Documentation': '📋',
    'Contingency': '🛡️',
    'Other': '📦'
  };

  return categoryIcons[category] || '💰';
};

// Budget status helpers
export const getBudgetStatus = (summary: BudgetSummary): {
  status: 'under_budget' | 'on_track' | 'over_budget' | 'no_budget';
  color: string;
  message: string;
} => {
  if (summary.total_budget === 0) {
    return {
      status: 'no_budget',
      color: 'text-gray-500',
      message: 'No budget set'
    };
  }

  const percentage = (summary.total_spent / summary.total_budget) * 100;

  if (percentage < 80) {
    return {
      status: 'under_budget',
      color: 'text-green-600',
      message: 'Under budget'
    };
  } else if (percentage <= 100) {
    return {
      status: 'on_track',
      color: 'text-yellow-600',
      message: 'On track'
    };
  } else {
    return {
      status: 'over_budget',
      color: 'text-red-600',
      message: 'Over budget'
    };
  }
};

// Budget export helpers
export const prepareBudgetForExport = (budget: Budget, items: BudgetItem[]) => {
  const summary = calculateBudgetSummary(budget, items);
  
  return {
    budgetInfo: {
      totalBudget: budget.total_budget,
      currency: budget.currency,
      createdAt: budget.created_at,
      updatedAt: budget.updated_at
    },
    summary: {
      totalEstimated: summary.total_estimated,
      totalActual: summary.total_actual,
      totalSpent: summary.total_spent,
      remainingBudget: summary.remaining_budget
    },
    items: items.map(item => ({
      category: item.category,
      estimatedCost: item.estimated_cost,
      actualCost: item.actual_cost,
      notes: item.notes,
      isCompleted: item.is_completed
    }))
  };
};

// Excel export function
export const exportBudgetToExcel = async (
  budget: Budget,
  items: BudgetItem[],
  summary?: BudgetSummary | null
): Promise<void> => {
  // Create CSV content as a simple alternative to Excel
  const csvContent = generateBudgetCSV(budget, items, summary);

  // Create and download the file
  const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
  const link = document.createElement('a');

  if (link.download !== undefined) {
    const url = URL.createObjectURL(blob);
    link.setAttribute('href', url);
    link.setAttribute('download', `budget-export-${new Date().toISOString().split('T')[0]}.csv`);
    link.style.visibility = 'hidden';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  }
};

// Helper function to generate CSV content
const generateBudgetCSV = (
  budget: Budget,
  items: BudgetItem[],
  summary?: BudgetSummary | null
): string => {
  const lines: string[] = [];

  // Budget summary
  lines.push('Budget Summary');
  lines.push(`Total Budget,${formatCurrency(budget.total_budget, budget.currency)}`);
  if (summary) {
    lines.push(`Total Estimated,${formatCurrency(summary.total_estimated, budget.currency)}`);
    lines.push(`Total Spent,${formatCurrency(summary.total_spent, budget.currency)}`);
    lines.push(`Remaining,${formatCurrency(summary.remaining_budget, budget.currency)}`);
  }
  lines.push(''); // Empty line

  // Budget items header
  lines.push('Budget Items');
  lines.push('Category,Estimated Cost,Actual Cost,Status,Notes');

  // Budget items data
  items.forEach(item => {
    const estimatedCost = formatCurrency(item.estimated_cost, budget.currency);
    const actualCost = item.actual_cost ? formatCurrency(item.actual_cost, budget.currency) : '';
    const status = item.is_completed ? 'Complete' : 'Pending';
    const notes = (item.notes || '').replace(/,/g, ';'); // Replace commas to avoid CSV issues

    lines.push(`"${item.category}","${estimatedCost}","${actualCost}","${status}","${notes}"`);
  });

  return lines.join('\n');
};