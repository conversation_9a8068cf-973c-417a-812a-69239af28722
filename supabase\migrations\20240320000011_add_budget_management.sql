-- Create budgets table
CREATE TABLE budgets (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID REFERENCES auth.users(id) NOT NULL,
    total_budget DECIMAL(12,2) NOT NULL CHECK (total_budget >= 0),
    currency TEXT DEFAULT 'UGX' NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT TIMEZONE('utc'::text, NOW()) NOT NULL,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT TIMEZONE('utc'::text, NOW()) NOT NULL,
    UNIQUE(user_id) -- One budget per user
);

-- Create budget_items table
CREATE TABLE budget_items (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    budget_id UUID REFERENCES budgets(id) ON DELETE CASCADE NOT NULL,
    category TEXT NOT NULL,
    estimated_cost DECIMAL(12,2) NOT NULL CHECK (estimated_cost >= 0),
    actual_cost DECIMAL(12,2) CHECK (actual_cost >= 0),
    notes TEXT,
    is_completed BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT TIMEZONE('utc'::text, NOW()) NOT NULL,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT TIMEZONE('utc'::text, NOW()) NOT NULL
);

-- Enable Row Level Security
ALTER TABLE budgets ENABLE ROW LEVEL SECURITY;
ALTER TABLE budget_items ENABLE ROW LEVEL SECURITY;

-- Budget policies
CREATE POLICY "Users can view their own budget"
    ON budgets FOR SELECT
    USING (auth.uid() = user_id);

CREATE POLICY "Users can update their own budget"
    ON budgets FOR UPDATE
    USING (auth.uid() = user_id);

CREATE POLICY "Users can insert their own budget"
    ON budgets FOR INSERT
    WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can delete their own budget"
    ON budgets FOR DELETE
    USING (auth.uid() = user_id);

-- Budget items policies
CREATE POLICY "Users can view their own budget items"
    ON budget_items FOR SELECT
    USING (
        EXISTS (
            SELECT 1 FROM budgets 
            WHERE budgets.id = budget_items.budget_id 
            AND budgets.user_id = auth.uid()
        )
    );

CREATE POLICY "Users can update their own budget items"
    ON budget_items FOR UPDATE
    USING (
        EXISTS (
            SELECT 1 FROM budgets 
            WHERE budgets.id = budget_items.budget_id 
            AND budgets.user_id = auth.uid()
        )
    );

CREATE POLICY "Users can insert their own budget items"
    ON budget_items FOR INSERT
    WITH CHECK (
        EXISTS (
            SELECT 1 FROM budgets 
            WHERE budgets.id = budget_items.budget_id 
            AND budgets.user_id = auth.uid()
        )
    );

CREATE POLICY "Users can delete their own budget items"
    ON budget_items FOR DELETE
    USING (
        EXISTS (
            SELECT 1 FROM budgets 
            WHERE budgets.id = budget_items.budget_id 
            AND budgets.user_id = auth.uid()
        )
    );

-- Grant necessary permissions
GRANT SELECT, INSERT, UPDATE, DELETE ON budgets TO authenticated;
GRANT SELECT, INSERT, UPDATE, DELETE ON budget_items TO authenticated;

-- Create triggers for updated_at
CREATE TRIGGER handle_budgets_updated_at
    BEFORE UPDATE ON budgets
    FOR EACH ROW
    EXECUTE FUNCTION handle_updated_at();

CREATE TRIGGER handle_budget_items_updated_at
    BEFORE UPDATE ON budget_items
    FOR EACH ROW
    EXECUTE FUNCTION handle_updated_at();

-- Create indexes for better performance
CREATE INDEX idx_budgets_user_id ON budgets(user_id);
CREATE INDEX idx_budget_items_budget_id ON budget_items(budget_id);
CREATE INDEX idx_budget_items_category ON budget_items(category);

-- Function to get budget summary
CREATE OR REPLACE FUNCTION get_budget_summary(user_uuid UUID)
RETURNS TABLE (
    total_budget DECIMAL(12,2),
    total_estimated DECIMAL(12,2),
    total_actual DECIMAL(12,2),
    total_spent DECIMAL(12,2),
    remaining_budget DECIMAL(12,2)
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        b.total_budget,
        COALESCE(SUM(bi.estimated_cost), 0) as total_estimated,
        COALESCE(SUM(bi.actual_cost), 0) as total_actual,
        COALESCE(SUM(CASE WHEN bi.actual_cost IS NOT NULL THEN bi.actual_cost ELSE 0 END), 0) as total_spent,
        b.total_budget - COALESCE(SUM(CASE WHEN bi.actual_cost IS NOT NULL THEN bi.actual_cost ELSE 0 END), 0) as remaining_budget
    FROM budgets b
    LEFT JOIN budget_items bi ON b.id = bi.budget_id
    WHERE b.user_id = user_uuid
    GROUP BY b.id, b.total_budget;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER; 