// SEO and Social Media Meta Tag Utilities
import React from 'react';

export interface SEOData {
  title: string;
  description: string;
  keywords?: string;
  image?: string;
  url?: string;
  type?: 'website' | 'article' | 'profile';
  author?: string;
  publishedTime?: string;
  modifiedTime?: string;
}

export const defaultSEO: SEOData = {
  title: "PledgeForLove | Digital Wedding Contribution Platform Uganda",
  description: "Uganda's premier digital wedding platform. Create beautiful online pledge cards, manage ceremony budgets, track contributions, and simplify your wedding planning. Supporting traditional Kukyala, Nikah, Kuhingira, and modern weddings.",
  keywords: "wedding Uganda, wedding contributions, pledge cards, wedding gifts, digital pledge, wedding planning, online wedding registry, wedding donations, Kukyala, Nikah, Ku<PERSON><PERSON>, traditional wedding, ceremony budget, wedding budget planning",
  image: "https://p4love.com/images/love.jpg",
  url: "https://p4love.com",
  type: "website",
  author: "PledgeForLove"
};

export const pageSEO: Record<string, SEOData> = {
  home: {
    title: "PledgeForLove | Digital Wedding Platform Uganda - Pledge Cards & Budget Management",
    description: "Uganda's complete digital wedding solution. Create beautiful pledge cards, manage ceremony budgets, and track contributions for Wedding, Kukyala, Nikah, and <PERSON><PERSON><PERSON> ceremonies. Modern, secure, and culturally aware.",
    keywords: "digital pledge cards Uganda, wedding contributions, online wedding registry, wedding planning, ceremony budget, Kukyala, Nikah, Kuhingira, traditional wedding Uganda",
    type: "website"
  },
  auth: {
    title: "Sign In | PledgeForLove",
    description: "Access your PledgeForLove account to manage wedding contributions and pledge cards. Secure login for wedding planning platform.",
    keywords: "login, sign in, wedding account, pledge management, wedding platform",
    type: "website"
  },
  dashboard: {
    title: "Wedding Dashboard | PledgeForLove",
    description: "Comprehensive wedding management dashboard. Track pledges, manage ceremony budgets, monitor contributions, and view payment status. Complete control center for your wedding planning.",
    keywords: "wedding dashboard, pledge management, contribution tracking, wedding planning, budget management, ceremony planning",
    type: "website"
  },
  "create-pledge": {
    title: "Create Pledge | PledgeForLove",
    description: "Create beautiful digital pledge cards for your wedding. Easy-to-use form for setting up wedding contributions and guest pledges.",
    keywords: "create pledge, wedding pledge card, digital wedding invitation, wedding contributions",
    type: "website"
  },
  profile: {
    title: "Profile | PledgeForLove",
    description: "Manage your PledgeForLove profile settings, wedding details, and account preferences. Personalize your wedding experience.",
    keywords: "user profile, wedding settings, account management, wedding profile",
    type: "profile"
  },
  "profile-setup": {
    title: "Complete Your Profile | PledgeForLove",
    description: "Set up your wedding profile with ceremony type, customize your pledge card theme, and configure privacy settings. Choose from Wedding, Kukyala, Nikah, or Kuhingira ceremonies.",
    keywords: "profile setup, wedding profile, account setup, pledge card customization, wedding setup, ceremony type, Kukyala, Nikah, Kuhingira",
    type: "website"
  },
  budget: {
    title: "Wedding Budget Management | PledgeForLove",
    description: "Comprehensive wedding budget planning tool for Uganda. Manage ceremony-specific budgets for Wedding, Kukyala, Nikah, and Kuhingira with cultural guidance and expense tracking.",
    keywords: "wedding budget Uganda, ceremony budget planning, Kukyala budget, Nikah budget, Kuhingira budget, wedding expenses, budget tracker, wedding cost planning",
    type: "website"
  },
  "budget-setup": {
    title: "Create Wedding Budget | PledgeForLove",
    description: "Set up your ceremony budget with our guided wizard. Get cultural guidance for traditional ceremonies and modern weddings. Track expenses and manage your wedding finances.",
    keywords: "create wedding budget, budget wizard, ceremony budget setup, wedding financial planning, budget categories",
    type: "website"
  },
  faq: {
    title: "Frequently Asked Questions | PledgeForLove",
    description: "Get answers to common questions about PledgeForLove's digital wedding platform, pledge cards, budget management, and ceremony support in Uganda.",
    keywords: "wedding platform FAQ, pledge card questions, budget help, ceremony support, wedding planning help Uganda",
    type: "website"
  },
  admin: {
    title: "Admin Panel | PledgeForLove",
    description: "Administrative dashboard for managing PledgeForLove platform users, monitoring system health, and platform analytics.",
    keywords: "admin panel, platform management, user administration, system monitoring",
    type: "website"
  }
};

// Function to update document meta tags dynamically
export const updateMetaTags = (seoData: Partial<SEOData>) => {
  const data = { ...defaultSEO, ...seoData };
  
  // Update title
  document.title = data.title;
  
  // Update or create meta tags
  const updateMetaTag = (name: string, content: string, property = false) => {
    const selector = property ? `meta[property="${name}"]` : `meta[name="${name}"]`;
    let meta = document.querySelector(selector) as HTMLMetaElement;
    
    if (!meta) {
      meta = document.createElement('meta');
      if (property) {
        meta.setAttribute('property', name);
      } else {
        meta.setAttribute('name', name);
      }
      document.head.appendChild(meta);
    }
    
    meta.setAttribute('content', content);
  };
  
  // Basic meta tags
  updateMetaTag('description', data.description);
  if (data.keywords) updateMetaTag('keywords', data.keywords);
  if (data.author) updateMetaTag('author', data.author);
  
  // Open Graph tags
  updateMetaTag('og:title', data.title, true);
  updateMetaTag('og:description', data.description, true);
  updateMetaTag('og:type', data.type || 'website', true);
  if (data.url) updateMetaTag('og:url', data.url, true);
  if (data.image) {
    updateMetaTag('og:image', data.image, true);
    updateMetaTag('og:image:alt', `${data.title} - Preview Image`, true);
  }
  
  // Twitter Card tags
  updateMetaTag('twitter:title', data.title);
  updateMetaTag('twitter:description', data.description);
  if (data.image) {
    updateMetaTag('twitter:image', data.image);
    updateMetaTag('twitter:image:alt', `${data.title} - Preview Image`);
  }
  if (data.url) updateMetaTag('twitter:url', data.url);
  
  // Article specific tags
  if (data.type === 'article') {
    if (data.publishedTime) updateMetaTag('article:published_time', data.publishedTime, true);
    if (data.modifiedTime) updateMetaTag('article:modified_time', data.modifiedTime, true);
    if (data.author) updateMetaTag('article:author', data.author, true);
  }
};

// Hook for React components to update SEO
export const useSEO = (pageKey: string, customData?: Partial<SEOData>) => {
  const seoData = pageSEO[pageKey] || defaultSEO;

  const finalData = React.useMemo(() => {
    return { ...seoData, ...customData };
  }, [seoData, customData]);

  // Update meta tags when component mounts or data changes
  React.useEffect(() => {
    updateMetaTags(finalData);

    // Cleanup function to reset to default when component unmounts
    return () => {
      if (pageKey !== 'home') {
        updateMetaTags(defaultSEO);
      }
    };
  }, [pageKey, finalData]);
};

// Generate structured data for rich snippets
export const generateStructuredData = (type: 'Organization' | 'WebSite' | 'Event' | 'Service' | 'SoftwareApplication' | 'FAQPage', data: Record<string, unknown> = {}) => {
  const baseStructure = {
    "@context": "https://schema.org",
    "@type": type
  };
  
  switch (type) {
    case 'Organization':
      return {
        ...baseStructure,
        name: "PledgeForLove",
        url: "https://p4love.com",
        logo: "https://p4love.com/images/logo.png",
        description: "Digital wedding contribution platform",
        contactPoint: {
          "@type": "ContactPoint",
          contactType: "customer service",
          email: "<EMAIL>"
        },
        sameAs: [
          "https://twitter.com/PledgeForLove",
          "https://facebook.com/PledgeForLove"
        ],
        ...data
      };
      
    case 'WebSite':
      return {
        ...baseStructure,
        name: "PledgeForLove",
        url: "https://p4love.com",
        description: "Digital wedding contribution platform",
        publisher: {
          "@type": "Organization",
          name: "PledgeForLove"
        },
        potentialAction: {
          "@type": "SearchAction",
          target: "https://p4love.com/search?q={search_term_string}",
          "query-input": "required name=search_term_string"
        },
        ...data
      };
      
    case 'Event':
      return {
        ...baseStructure,
        name: data.name || "Wedding Event",
        description: data.description || "Wedding celebration with digital pledge contributions",
        startDate: data.startDate,
        endDate: data.endDate,
        location: {
          "@type": "Place",
          name: data.location || "Wedding Venue",
          address: {
            "@type": "PostalAddress",
            addressCountry: data.addressCountry || "US"
          }
        },
        organizer: {
          "@type": "Person",
          name: data.organizer || "Wedding Couple"
        },
        ...data
      };

    case 'Service':
      return {
        ...baseStructure,
        name: "Digital Wedding Contribution Platform",
        description: "Professional digital platform for managing wedding contributions and pledge cards",
        provider: {
          "@type": "Organization",
          name: "PledgeForLove"
        },
        serviceType: "Wedding Planning Service",
        category: "Wedding Services",
        offers: {
          "@type": "Offer",
          description: "Digital wedding contribution management",
          price: "0",
          priceCurrency: "USD"
        },
        ...data
      };

    case 'SoftwareApplication':
      return {
        ...baseStructure,
        name: "PledgeForLove",
        description: "Digital wedding contribution platform for creating and managing pledge cards",
        applicationCategory: "WebApplication",
        operatingSystem: "Web Browser",
        offers: {
          "@type": "Offer",
          price: "0",
          priceCurrency: "USD"
        },
        author: {
          "@type": "Organization",
          name: "PledgeForLove"
        },
        aggregateRating: {
          "@type": "AggregateRating",
          ratingValue: "4.8",
          ratingCount: "150",
          bestRating: "5",
          worstRating: "1"
        },
        ...data
      };

    case 'FAQPage':
      return {
        ...baseStructure,
        mainEntity: data.questions || [
          {
            "@type": "Question",
            name: "How does PledgeForLove work?",
            acceptedAnswer: {
              "@type": "Answer",
              text: "PledgeForLove allows couples to create digital pledge cards for their wedding, making it easy for guests to contribute and track payments online."
            }
          },
          {
            "@type": "Question",
            name: "Is PledgeForLove free to use?",
            acceptedAnswer: {
              "@type": "Answer",
              text: "Yes, PledgeForLove is completely free to use for creating and managing wedding pledge cards."
            }
          },
          {
            "@type": "Question",
            name: "Can I use PledgeForLove for my wedding?",
            acceptedAnswer: {
              "@type": "Answer",
              text: "Absolutely! PledgeForLove is designed for weddings and supports various payment methods and traditions."
            }
          }
        ],
        ...data
      };

    default:
      return baseStructure;
  }
};

// Function to inject structured data into the page
export const injectStructuredData = (structuredData: Record<string, unknown>) => {
  const script = document.createElement('script');
  script.type = 'application/ld+json';
  script.textContent = JSON.stringify(structuredData);
  
  // Remove existing structured data script if present
  const existingScript = document.querySelector('script[type="application/ld+json"]');
  if (existingScript) {
    existingScript.remove();
  }
  
  document.head.appendChild(script);
};
