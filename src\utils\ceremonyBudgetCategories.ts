import type { CeremonyType, CeremonyBudgetTemplate, CeremonyBudgetCategory } from '@/types/app';

// Ceremony-specific budget categories
export const ceremonyBudgetCategories: Record<CeremonyType, CeremonyBudgetCategory[]> = {
  wedding: [
    {
      name: 'Venue & Catering',
      description: 'Reception venue, food, and beverages',
      isEssential: true,
      typicalPercentage: 40,
      culturalNotes: 'Often the largest expense for modern weddings'
    },
    {
      name: 'Photography & Videography',
      description: 'Professional photography and video services',
      isEssential: true,
      typicalPercentage: 10,
      culturalNotes: 'Captures memories for a lifetime'
    },
    {
      name: 'Attire & Accessories',
      description: 'Wedding dress, suit, shoes, and accessories',
      isEssential: true,
      typicalPercentage: 8,
      culturalNotes: 'Traditional and modern wedding attire'
    },
    {
      name: 'Decoration & Flowers',
      description: 'Floral arrangements and venue decorations',
      isEssential: true,
      typicalPercentage: 8,
      culturalNotes: 'Creates the wedding atmosphere'
    },
    {
      name: 'Music & Entertainment',
      description: 'DJ, band, or live entertainment',
      isEssential: true,
      typicalPercentage: 8,
      culturalNotes: 'Sets the mood for celebration'
    },
    {
      name: 'Transportation',
      description: 'Wedding car, transport for guests',
      isEssential: false,
      typicalPercentage: 3,
      culturalNotes: 'Special transport for the couple'
    },
    {
      name: 'Wedding Rings',
      description: 'Exchange rings for the ceremony',
      isEssential: true,
      typicalPercentage: 3,
      culturalNotes: 'Symbol of eternal commitment'
    },
    {
      name: 'Beauty & Grooming',
      description: 'Hair, makeup, and grooming services',
      isEssential: true,
      typicalPercentage: 5,
      culturalNotes: 'Looking perfect for the special day'
    },
    {
      name: 'Wedding Cake',
      description: 'Wedding cake and desserts',
      isEssential: true,
      typicalPercentage: 2,
      culturalNotes: 'Traditional cake cutting ceremony'
    },
    {
      name: 'Stationery & Invitations',
      description: 'Wedding invitations and stationery',
      isEssential: true,
      typicalPercentage: 2,
      culturalNotes: 'Formal invitations to guests'
    },
    {
      name: 'Legal & Documentation',
      description: 'Marriage license and legal fees',
      isEssential: true,
      typicalPercentage: 1,
      culturalNotes: 'Legal requirements for marriage'
    },
    {
      name: 'Gifts & Favors',
      description: 'Wedding favors and gifts for guests',
      isEssential: false,
      typicalPercentage: 3,
      culturalNotes: 'Thank you gifts for guests'
    },
    {
      name: 'Contingency',
      description: 'Emergency fund for unexpected expenses',
      isEssential: true,
      typicalPercentage: 5,
      culturalNotes: 'Buffer for unexpected costs'
    },
    {
      name: 'Other',
      description: 'Miscellaneous wedding expenses',
      isEssential: false,
      typicalPercentage: 2,
      culturalNotes: 'Additional expenses not covered above'
    }
  ],

  kukyala: [
    {
      name: 'Traditional Attire',
      description: 'Cultural clothing for both families',
      isEssential: true,
      typicalPercentage: 20,
      culturalNotes: 'Proper traditional dress shows respect'
    },
    {
      name: 'Gifts for Bride\'s Family',
      description: 'Traditional gifts and bride price items',
      isEssential: true,
      typicalPercentage: 30,
      culturalNotes: 'Shows appreciation and respect to bride\'s family'
    },
    {
      name: 'Traditional Drinks',
      description: 'Local brews and traditional beverages',
      isEssential: true,
      typicalPercentage: 15,
      culturalNotes: 'Essential for traditional ceremonies'
    },
    {
      name: 'Cultural Decorations',
      description: 'Traditional decorations and setup',
      isEssential: true,
      typicalPercentage: 8,
      culturalNotes: 'Creates authentic cultural atmosphere'
    },
    {
      name: 'Traditional Music',
      description: 'Cultural musicians and entertainment',
      isEssential: true,
      typicalPercentage: 10,
      culturalNotes: 'Traditional songs and dances'
    },
    {
      name: 'Transportation',
      description: 'Transport for families and guests',
      isEssential: false,
      typicalPercentage: 5,
      culturalNotes: 'Ensuring all family members can attend'
    },
    {
      name: 'Photography',
      description: 'Capturing the traditional ceremony',
      isEssential: false,
      typicalPercentage: 5,
      culturalNotes: 'Preserving cultural memories'
    },
    {
      name: 'Ceremony Venue',
      description: 'Traditional venue or family compound',
      isEssential: true,
      typicalPercentage: 3,
      culturalNotes: 'Appropriate setting for the ceremony'
    },
    {
      name: 'Traditional Food',
      description: 'Cultural dishes and refreshments',
      isEssential: true,
      typicalPercentage: 2,
      culturalNotes: 'Sharing traditional meals'
    },
    {
      name: 'Cultural Advisor',
      description: 'Elder or cultural guide fees',
      isEssential: false,
      typicalPercentage: 2,
      culturalNotes: 'Guidance on proper cultural protocols'
    }
  ],

  nikah: [
    {
      name: 'Mahr (Dower)',
      description: 'Mandatory gift from groom to bride',
      isEssential: true,
      typicalPercentage: 25,
      culturalNotes: 'Islamic requirement, amount agreed by both parties'
    },
    {
      name: 'Mosque/Venue Fees',
      description: 'Venue for the Nikah ceremony',
      isEssential: true,
      typicalPercentage: 5,
      culturalNotes: 'Sacred space for the Islamic ceremony'
    },
    {
      name: 'Islamic Attire',
      description: 'Modest Islamic wedding clothing',
      isEssential: true,
      typicalPercentage: 15,
      culturalNotes: 'Modest and appropriate Islamic dress'
    },
    {
      name: 'Walima Preparation',
      description: 'Reception feast after Nikah',
      isEssential: true,
      typicalPercentage: 30,
      culturalNotes: 'Sunnah to celebrate with community'
    },
    {
      name: 'Religious Decorations',
      description: 'Islamic-themed decorations',
      isEssential: false,
      typicalPercentage: 5,
      culturalNotes: 'Beautiful but modest decorations'
    },
    {
      name: 'Halal Catering',
      description: 'Halal food and beverages',
      isEssential: true,
      typicalPercentage: 10,
      culturalNotes: 'All food must be halal certified'
    },
    {
      name: 'Islamic Music/Nasheed',
      description: 'Religious songs and entertainment',
      isEssential: false,
      typicalPercentage: 3,
      culturalNotes: 'Islamic songs and nasheeds only'
    },
    {
      name: 'Transportation',
      description: 'Transport for families and guests',
      isEssential: false,
      typicalPercentage: 3,
      culturalNotes: 'Ensuring community can attend'
    },
    {
      name: 'Photography',
      description: 'Modest photography services',
      isEssential: false,
      typicalPercentage: 2,
      culturalNotes: 'Respectful documentation of the ceremony'
    },
    {
      name: 'Religious Documentation',
      description: 'Nikah certificate and legal fees',
      isEssential: true,
      typicalPercentage: 2,
      culturalNotes: 'Official Islamic marriage documentation'
    }
  ],

  kuhingira: [
    {
      name: 'Traditional Ceremony Venue',
      description: 'Sacred or cultural venue for ceremony',
      isEssential: true,
      typicalPercentage: 15,
      culturalNotes: 'Appropriate traditional setting'
    },
    {
      name: 'Cultural Attire',
      description: 'Traditional ceremonial clothing',
      isEssential: true,
      typicalPercentage: 20,
      culturalNotes: 'Authentic traditional dress for all participants'
    },
    {
      name: 'Traditional Gifts',
      description: 'Ceremonial gifts and offerings',
      isEssential: true,
      typicalPercentage: 25,
      culturalNotes: 'Gifts that honor cultural traditions'
    },
    {
      name: 'Cultural Decorations',
      description: 'Traditional decorations and setup',
      isEssential: true,
      typicalPercentage: 10,
      culturalNotes: 'Authentic cultural atmosphere'
    },
    {
      name: 'Traditional Entertainment',
      description: 'Cultural music, dance, and performances',
      isEssential: true,
      typicalPercentage: 12,
      culturalNotes: 'Traditional songs and cultural performances'
    },
    {
      name: 'Ceremonial Items',
      description: 'Traditional items needed for rituals',
      isEssential: true,
      typicalPercentage: 8,
      culturalNotes: 'Sacred items for traditional rituals'
    },
    {
      name: 'Photography & Documentation',
      description: 'Capturing the cultural ceremony',
      isEssential: false,
      typicalPercentage: 3,
      culturalNotes: 'Preserving cultural heritage'
    },
    {
      name: 'Traditional Food',
      description: 'Cultural dishes and ceremonial meals',
      isEssential: true,
      typicalPercentage: 5,
      culturalNotes: 'Traditional foods for the ceremony'
    },
    {
      name: 'Transportation',
      description: 'Transport for participants and guests',
      isEssential: false,
      typicalPercentage: 1,
      culturalNotes: 'Ensuring all can participate'
    },
    {
      name: 'Cultural Guide',
      description: 'Elder or cultural expert guidance',
      isEssential: false,
      typicalPercentage: 1,
      culturalNotes: 'Proper execution of cultural protocols'
    }
  ]
};

// Ceremony templates with full information
export const ceremonyBudgetTemplates: Record<CeremonyType, CeremonyBudgetTemplate> = {
  wedding: {
    ceremonyType: 'wedding',
    displayName: 'Wedding',
    description: 'Modern wedding ceremony with traditional elements',
    categories: ceremonyBudgetCategories.wedding,
    culturalNotes: 'Modern weddings often blend traditional and contemporary elements. Budget allocation should prioritize venue, catering, and photography as these create lasting memories.',
    typicalRange: { min: 5000000, max: 100000000 }, // 5M - 100M UGX
    currency: 'UGX'
  },
  kukyala: {
    ceremonyType: 'kukyala',
    displayName: 'Kukyala (Introduction Ceremony)',
    description: 'Traditional introduction ceremony between families',
    categories: ceremonyBudgetCategories.kukyala,
    culturalNotes: 'Kukyala is a formal introduction where the groom\'s family meets the bride\'s family. Focus on respect, tradition, and proper cultural protocols. Gifts and traditional attire are essential.',
    typicalRange: { min: 2000000, max: 20000000 }, // 2M - 20M UGX
    currency: 'UGX'
  },
  nikah: {
    ceremonyType: 'nikah',
    displayName: 'Nikah (Islamic Wedding)',
    description: 'Islamic marriage ceremony following Islamic traditions',
    categories: ceremonyBudgetCategories.nikah,
    culturalNotes: 'Nikah is the Islamic marriage contract ceremony. Mahr is mandatory and should be agreed upon beforehand. All aspects must comply with Islamic principles including halal food and modest celebrations.',
    typicalRange: { min: 3000000, max: 20000000 }, // 3M - 20M UGX
    currency: 'UGX'
  },
  kuhingira: {
    ceremonyType: 'kuhingira',
    displayName: 'Kuhingira (Traditional Ceremony)',
    description: 'Traditional cultural ceremony honoring ancestral customs',
    categories: ceremonyBudgetCategories.kuhingira,
    culturalNotes: 'Kuhingira honors traditional customs and ancestral practices. Emphasis on authentic cultural elements, traditional attire, and proper ceremonial protocols. Community involvement is important.',
    typicalRange: { min: 2000000, max: 20000000 }, // 2M - 20M UGX
    currency: 'UGX'
  }
};

// Helper functions
export const getCeremonyBudgetTemplate = (ceremonyType: CeremonyType): CeremonyBudgetTemplate => {
  return ceremonyBudgetTemplates[ceremonyType];
};

export const getCeremonyCategories = (ceremonyType: CeremonyType): CeremonyBudgetCategory[] => {
  return ceremonyBudgetCategories[ceremonyType];
};

export const getCeremonyDisplayName = (ceremonyType: CeremonyType): string => {
  return ceremonyBudgetTemplates[ceremonyType].displayName;
};

export const getAllCeremonyTypes = (): CeremonyType[] => {
  return Object.keys(ceremonyBudgetTemplates) as CeremonyType[];
};

// Backward compatibility - return wedding categories as default
export const getDefaultBudgetCategories = (): string[] => {
  return ceremonyBudgetCategories.wedding.map(category => category.name);
};
