# Service Provider Directory - Technical Debt Analysis & Implementation Strategy

## 📊 **TECHNICAL DEBT ASSESSMENT**

### **Complexity Rating: HIGH (8/10)**
- **Database Schema**: 7 new tables with complex relationships
- **Authentication**: Dual user types (couples + vendors)
- **UI/UX Integration**: Significant navigation and component changes
- **Business Logic**: Rating systems, booking workflows, payment integration

### **Development Effort Estimation**

#### **Phase 1: Foundation (4-6 weeks)**
- Database schema design and migration
- Vendor authentication system
- Basic vendor profile management
- Core API endpoints

#### **Phase 2: Integration (3-4 weeks)**
- Budget-to-vendor connection system
- Search and filtering functionality
- Rating and review system
- Mobile-responsive vendor browsing

#### **Phase 3: Advanced Features (2-3 weeks)**
- Booking workflow integration
- Payment processing (if applicable)
- Advanced search with location/ceremony type
- Vendor dashboard and analytics

#### **Total Estimated Effort: 9-13 weeks**

### **Risk Assessment**

#### **HIGH RISKS:**
1. **User Experience Complexity**: Risk of overwhelming core pledge/budget experience
2. **Data Integrity**: Complex relationships between budgets, vendors, and bookings
3. **Performance**: Large vendor datasets affecting search and browse performance
4. **Authentication Complexity**: Managing two distinct user types

#### **MEDIUM RISKS:**
1. **Mobile Experience**: Vendor browsing on mobile devices
2. **Search Performance**: Filtering vendors by location, ceremony type, budget
3. **Content Moderation**: Vendor profiles, reviews, and inappropriate content

#### **LOW RISKS:**
1. **SEO Impact**: Additional pages may improve search visibility
2. **Scalability**: Current architecture can handle additional features

---

## 🏗️ **SEAMLESS INTEGRATION STRATEGY**

### **1. Database Schema Integration**

#### **New Tables Required:**
```sql
-- Vendor profiles
CREATE TABLE vendor_profiles (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    business_name TEXT NOT NULL,
    business_type TEXT NOT NULL, -- 'photographer', 'caterer', 'venue', etc.
    description TEXT,
    location TEXT,
    phone TEXT,
    website TEXT,
    portfolio_images TEXT[], -- Array of image URLs
    ceremony_types TEXT[] DEFAULT ARRAY['wedding'], -- Supported ceremony types
    price_range_min DECIMAL(12,2),
    price_range_max DECIMAL(12,2),
    currency TEXT DEFAULT 'UGX',
    is_verified BOOLEAN DEFAULT FALSE,
    rating_average DECIMAL(3,2) DEFAULT 0,
    rating_count INTEGER DEFAULT 0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Vendor services (connects to budget categories)
CREATE TABLE vendor_services (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    vendor_id UUID REFERENCES vendor_profiles(id) ON DELETE CASCADE,
    budget_category TEXT NOT NULL, -- Links to budget categories
    service_name TEXT NOT NULL,
    description TEXT,
    base_price DECIMAL(12,2),
    price_unit TEXT, -- 'per_person', 'per_hour', 'fixed', etc.
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Vendor reviews and ratings
CREATE TABLE vendor_reviews (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    vendor_id UUID REFERENCES vendor_profiles(id) ON DELETE CASCADE,
    reviewer_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    rating INTEGER CHECK (rating >= 1 AND rating <= 5),
    review_text TEXT,
    ceremony_type TEXT,
    service_used TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(vendor_id, reviewer_id) -- One review per vendor per user
);

-- Budget item vendor connections
CREATE TABLE budget_vendor_connections (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    budget_item_id UUID REFERENCES wedding_budget_items(id) ON DELETE CASCADE,
    vendor_id UUID REFERENCES vendor_profiles(id) ON DELETE CASCADE,
    service_id UUID REFERENCES vendor_services(id) ON DELETE SET NULL,
    status TEXT DEFAULT 'interested', -- 'interested', 'contacted', 'booked', 'completed'
    notes TEXT,
    quoted_price DECIMAL(12,2),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

### **2. User Flow Integration**

#### **Budget-to-Vendor Discovery Flow:**
```
Budget Planning → Category Selection → "Find Vendors" Button → 
Filtered Vendor List → Vendor Profile → Connect to Budget Item → 
Track in Budget Dashboard
```

#### **Navigation Integration:**
- **Main Navigation**: Add "Vendors" tab (authenticated users only)
- **Budget Page**: "Find Vendors" buttons for each category
- **Dashboard**: "Recommended Vendors" section based on budget categories

### **3. Data Relationship Strategy**

#### **Core Connections:**
1. **Budget Categories ↔ Vendor Services**: Direct mapping
2. **Budget Items ↔ Vendor Connections**: Track vendor interest/booking
3. **User Profiles ↔ Vendor Recommendations**: Based on ceremony type and location
4. **Vendor Reviews ↔ User Profiles**: Verified reviews from actual users

---

## 🎨 **UX/UI INTEGRATION STRATEGY**

### **1. Navigation Structure (Non-Intrusive)**

#### **Desktop Header Addition:**
```typescript
// Add to existing navigation (after Budget)
{user && !isAdmin && (
  <Button
    variant="ghost"
    onClick={() => navigate('/vendors')}
    className="text-gray-700 hover:text-gray-900"
  >
    <Store className="h-4 w-4 mr-2" />
    Vendors
  </Button>
)}
```

#### **Mobile Navigation Addition:**
```typescript
// Add to MobileNavigation.tsx after Budget button
<SheetClose asChild>
  <Button
    variant="ghost"
    className="w-full justify-start h-11"
    onClick={() => handleNavigation('/vendors')}
  >
    <Store className="h-4 w-4 mr-3" />
    Find Vendors
  </Button>
</SheetClose>
```

### **2. Budget Integration Points**

#### **Budget Dashboard Enhancement:**
```typescript
// Add to each budget category card
<div className="flex justify-between items-center">
  <span>{category.name}</span>
  <div className="flex gap-2">
    <Button size="sm" variant="outline" onClick={() => findVendors(category.name)}>
      <Search className="h-3 w-3 mr-1" />
      Find Vendors
    </Button>
    <span>{formatCurrency(category.estimated_cost)}</span>
  </div>
</div>
```

#### **Vendor Suggestions in Budget:**
```typescript
// Show recommended vendors based on budget categories
{budgetItems.map(item => (
  <Card key={item.id}>
    <CardContent>
      <div className="flex justify-between">
        <div>
          <h3>{item.category}</h3>
          <p>Budget: {formatCurrency(item.estimated_cost)}</p>
        </div>
        {recommendedVendors[item.category] && (
          <div className="text-sm">
            <p className="text-gray-600">Recommended:</p>
            <Button variant="link" size="sm">
              {recommendedVendors[item.category].business_name}
            </Button>
          </div>
        )}
      </div>
    </CardContent>
  </Card>
))}
```

### **3. Mobile-First Vendor Browsing**

#### **Responsive Vendor Cards:**
```typescript
// Mobile-optimized vendor listing
<div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
  {vendors.map(vendor => (
    <Card key={vendor.id} className="hover:shadow-lg transition-shadow">
      <div className="aspect-video bg-gray-100 rounded-t-lg">
        <img 
          src={vendor.portfolio_images[0]} 
          alt={vendor.business_name}
          className="w-full h-full object-cover rounded-t-lg"
        />
      </div>
      <CardContent className="p-4">
        <div className="flex justify-between items-start mb-2">
          <h3 className="font-semibold text-lg">{vendor.business_name}</h3>
          <div className="flex items-center gap-1">
            <Star className="h-4 w-4 fill-yellow-400 text-yellow-400" />
            <span className="text-sm">{vendor.rating_average}</span>
          </div>
        </div>
        <p className="text-gray-600 text-sm mb-3">{vendor.description}</p>
        <div className="flex justify-between items-center">
          <span className="text-sm text-gray-500">
            {formatCurrency(vendor.price_range_min)} - {formatCurrency(vendor.price_range_max)}
          </span>
          <Button size="sm">View Details</Button>
        </div>
      </CardContent>
    </Card>
  ))}
</div>
```

---

## 🚀 **PHASED IMPLEMENTATION APPROACH**

### **Phase 1: Foundation & Core Integration (4-6 weeks)**

#### **Week 1-2: Database & Authentication**
```sql
-- File: supabase/migrations/20240320000013_add_vendor_system.sql
-- Create vendor tables with proper RLS policies
-- Add vendor role to user profiles
-- Create vendor authentication flows
```

#### **Week 3-4: Basic Vendor Management**
```typescript
// Files to create:
src/types/vendor.ts                    // Vendor type definitions
src/integrations/vendor.ts             // Vendor CRUD operations
src/components/vendor/VendorProfile.tsx // Vendor profile component
src/pages/VendorDashboard.tsx          // Vendor management page
```

#### **Week 5-6: Budget Integration**
```typescript
// Files to modify:
src/components/budget/BudgetItemManager.tsx  // Add "Find Vendors" buttons
src/components/budget/BudgetDashboard.tsx    // Add vendor recommendations
src/integrations/budget.ts                  // Add vendor connection methods
```

### **Phase 2: Search & Discovery (3-4 weeks)**

#### **Week 7-8: Vendor Directory**
```typescript
// Files to create:
src/pages/VendorDirectory.tsx          // Main vendor browsing page
src/components/vendor/VendorSearch.tsx // Search and filter component
src/components/vendor/VendorCard.tsx   // Individual vendor display
src/hooks/useVendorSearch.ts           // Search logic hook
```

#### **Week 9-10: Integration & Reviews**
```typescript
// Files to create:
src/components/vendor/VendorReviews.tsx     // Review system
src/components/vendor/VendorBooking.tsx     // Booking workflow
src/components/budget/VendorConnections.tsx // Budget-vendor connections
```

### **Phase 3: Advanced Features (2-3 weeks)**

#### **Week 11-12: Enhanced Features**
```typescript
// Files to create:
src/components/vendor/VendorAnalytics.tsx   // Vendor dashboard analytics
src/components/vendor/VendorPortfolio.tsx   // Portfolio management
src/hooks/useVendorRecommendations.ts       // AI-powered recommendations
```

#### **Week 13: Testing & Optimization**
- Performance optimization for vendor search
- Mobile experience refinement
- Integration testing with existing features
- User acceptance testing

---

## 📱 **MOBILE-RESPONSIVE DESIGN STRATEGY**

### **Navigation Adaptation**
- **Bottom Tab Bar**: Consider adding vendor tab for mobile
- **Swipe Gestures**: Implement swipe navigation for vendor browsing
- **Quick Actions**: Floating action button for "Find Vendors" in budget view

### **Vendor Browsing Optimization**
- **Card-based Layout**: Stack vendors vertically on mobile
- **Infinite Scroll**: Load vendors progressively
- **Quick Filters**: Collapsible filter panel
- **Touch-Friendly**: Large tap targets for vendor actions

---

## 🔧 **TECHNICAL IMPLEMENTATION DETAILS**

### **API Endpoints Required**
```typescript
// Vendor management
GET    /api/vendors                    // List vendors with filters
GET    /api/vendors/:id                // Get vendor details
POST   /api/vendors                    // Create vendor profile
PUT    /api/vendors/:id                // Update vendor profile
DELETE /api/vendors/:id                // Delete vendor profile

// Vendor services
GET    /api/vendors/:id/services       // Get vendor services
POST   /api/vendors/:id/services       // Add service
PUT    /api/services/:id               // Update service
DELETE /api/services/:id               // Delete service

// Reviews and ratings
GET    /api/vendors/:id/reviews        // Get vendor reviews
POST   /api/vendors/:id/reviews        // Add review
PUT    /api/reviews/:id                // Update review
DELETE /api/reviews/:id                // Delete review

// Budget integration
POST   /api/budget-items/:id/vendors   // Connect vendor to budget item
GET    /api/budget-items/:id/vendors   // Get connected vendors
DELETE /api/budget-vendors/:id         // Remove vendor connection
```

### **Component Architecture**
```
src/components/vendor/
├── VendorDirectory.tsx          # Main vendor listing page
├── VendorCard.tsx              # Individual vendor card
├── VendorProfile.tsx           # Detailed vendor profile
├── VendorSearch.tsx            # Search and filter component
├── VendorReviews.tsx           # Review system
├── VendorBooking.tsx           # Booking workflow
├── VendorPortfolio.tsx         # Portfolio gallery
├── VendorDashboard.tsx         # Vendor management dashboard
└── __tests__/                  # Component tests
```

---

## 🎯 **SUCCESS METRICS & MONITORING**

### **Key Performance Indicators**
1. **User Engagement**: Time spent in vendor directory
2. **Conversion Rate**: Budget items connected to vendors
3. **Vendor Adoption**: Number of vendor registrations
4. **Review Quality**: Average review rating and count
5. **Mobile Usage**: Vendor browsing on mobile devices

### **Technical Monitoring**
1. **Search Performance**: Vendor search response times
2. **Image Loading**: Portfolio image optimization
3. **Database Performance**: Complex query optimization
4. **Mobile Performance**: Page load times on mobile

---

## 🔄 **INTEGRATION WITH EXISTING SYSTEMS**

### **Budget System Integration Points**

#### **1. Enhanced Budget Categories**
```typescript
// Extend existing budget categories with vendor support
interface EnhancedBudgetCategory extends CeremonyBudgetCategory {
  vendorTypes: string[];           // Supported vendor business types
  averageVendorCount: number;      // Typical number of vendors needed
  bookingLeadTime: number;         // Days in advance to book
  priceNegotiable: boolean;        // Whether prices are typically negotiable
}

// Example enhancement for Photography category
{
  name: 'Photography & Videography',
  description: 'Professional photography and video services',
  isEssential: true,
  typicalPercentage: 10,
  culturalNotes: 'Captures memories for a lifetime',
  vendorTypes: ['photographer', 'videographer', 'photo_booth'],
  averageVendorCount: 2,
  bookingLeadTime: 60,
  priceNegotiable: true
}
```

#### **2. Smart Vendor Recommendations**
```typescript
// Algorithm for vendor recommendations based on budget and ceremony type
interface VendorRecommendationEngine {
  getBudgetBasedRecommendations(
    budgetItem: BudgetItem,
    ceremonyType: CeremonyType,
    location?: string
  ): Promise<VendorProfile[]>;

  getCeremonySpecificVendors(
    ceremonyType: CeremonyType,
    categories: string[]
  ): Promise<VendorProfile[]>;

  getLocationBasedVendors(
    location: string,
    radius: number,
    categories: string[]
  ): Promise<VendorProfile[]>;
}
```

### **Authentication System Extension**

#### **Dual User Type Management**
```typescript
// Extend existing profile system
interface UserProfile extends Tables<'profiles'> {
  user_type: 'couple' | 'vendor' | 'admin';
  vendor_profile?: VendorProfile;
}

// Vendor-specific authentication flow
interface VendorRegistrationData {
  // Basic business info
  business_name: string;
  business_type: VendorBusinessType;
  description: string;

  // Contact and location
  phone: string;
  email: string;
  location: string;
  website?: string;

  // Service details
  ceremony_types: CeremonyType[];
  service_categories: string[];
  price_range: { min: number; max: number };

  // Verification documents
  business_license?: File;
  portfolio_images: File[];
}
```

---

## 🎨 **DETAILED UI/UX SPECIFICATIONS**

### **Vendor Discovery Integration in Budget Flow**

#### **Budget Item Enhancement**
```typescript
// Enhanced budget item component with vendor integration
const BudgetItemWithVendors: React.FC<{item: BudgetItem}> = ({ item }) => {
  const [connectedVendors, setConnectedVendors] = useState<VendorConnection[]>([]);
  const [recommendedVendors, setRecommendedVendors] = useState<VendorProfile[]>([]);

  return (
    <Card className="budget-item-card">
      <CardHeader>
        <div className="flex justify-between items-center">
          <div>
            <h3>{item.category}</h3>
            <p className="text-sm text-gray-600">
              Budget: {formatCurrency(item.estimated_cost)}
            </p>
          </div>
          <div className="flex gap-2">
            <Button
              size="sm"
              variant="outline"
              onClick={() => openVendorSearch(item.category)}
            >
              <Search className="h-3 w-3 mr-1" />
              Find Vendors
            </Button>
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button size="sm" variant="ghost">
                  <MoreHorizontal className="h-4 w-4" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent>
                <DropdownMenuItem onClick={() => viewConnectedVendors()}>
                  View Connected ({connectedVendors.length})
                </DropdownMenuItem>
                <DropdownMenuItem onClick={() => getQuotes()}>
                  Request Quotes
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        </div>
      </CardHeader>

      {/* Connected Vendors Preview */}
      {connectedVendors.length > 0 && (
        <CardContent>
          <div className="space-y-2">
            <h4 className="text-sm font-medium">Connected Vendors:</h4>
            {connectedVendors.slice(0, 2).map(connection => (
              <div key={connection.id} className="flex items-center justify-between p-2 bg-gray-50 rounded">
                <div className="flex items-center gap-2">
                  <Avatar className="h-6 w-6">
                    <AvatarImage src={connection.vendor.logo} />
                    <AvatarFallback>{connection.vendor.business_name[0]}</AvatarFallback>
                  </Avatar>
                  <span className="text-sm">{connection.vendor.business_name}</span>
                  <Badge variant={connection.status === 'booked' ? 'default' : 'secondary'}>
                    {connection.status}
                  </Badge>
                </div>
                {connection.quoted_price && (
                  <span className="text-sm font-medium">
                    {formatCurrency(connection.quoted_price)}
                  </span>
                )}
              </div>
            ))}
            {connectedVendors.length > 2 && (
              <Button variant="link" size="sm" onClick={() => viewAllConnectedVendors()}>
                View all {connectedVendors.length} vendors
              </Button>
            )}
          </div>
        </CardContent>
      )}

      {/* Recommended Vendors Preview */}
      {recommendedVendors.length > 0 && connectedVendors.length === 0 && (
        <CardContent>
          <div className="space-y-2">
            <h4 className="text-sm font-medium">Recommended for you:</h4>
            <div className="flex gap-2 overflow-x-auto">
              {recommendedVendors.slice(0, 3).map(vendor => (
                <div key={vendor.id} className="flex-shrink-0 p-2 border rounded-lg min-w-[120px]">
                  <div className="text-xs font-medium truncate">{vendor.business_name}</div>
                  <div className="text-xs text-gray-500 flex items-center gap-1">
                    <Star className="h-3 w-3 fill-yellow-400 text-yellow-400" />
                    {vendor.rating_average}
                  </div>
                  <Button size="sm" variant="outline" className="w-full mt-1 text-xs">
                    Connect
                  </Button>
                </div>
              ))}
            </div>
          </div>
        </CardContent>
      )}
    </Card>
  );
};
```

### **Vendor Directory Page Layout**

#### **Mobile-First Responsive Design**
```typescript
const VendorDirectory: React.FC = () => {
  return (
    <div className="vendor-directory">
      {/* Header with Search */}
      <div className="sticky top-0 bg-white z-10 border-b">
        <div className="container mx-auto px-4 py-4">
          <div className="flex flex-col gap-4">
            <div className="flex items-center justify-between">
              <h1 className="text-2xl font-bold">Find Vendors</h1>
              <Button variant="outline" onClick={() => openFilters()}>
                <Filter className="h-4 w-4 mr-2" />
                Filters
              </Button>
            </div>

            {/* Search Bar */}
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
              <Input
                placeholder="Search vendors, services, or locations..."
                className="pl-10"
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
              />
            </div>

            {/* Quick Filters */}
            <div className="flex gap-2 overflow-x-auto pb-2">
              {quickFilters.map(filter => (
                <Button
                  key={filter.id}
                  size="sm"
                  variant={activeFilters.includes(filter.id) ? "default" : "outline"}
                  className="flex-shrink-0"
                  onClick={() => toggleFilter(filter.id)}
                >
                  {filter.label}
                </Button>
              ))}
            </div>
          </div>
        </div>
      </div>

      {/* Vendor Grid */}
      <div className="container mx-auto px-4 py-6">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
          {vendors.map(vendor => (
            <VendorCard key={vendor.id} vendor={vendor} />
          ))}
        </div>

        {/* Load More */}
        {hasMore && (
          <div className="text-center mt-8">
            <Button onClick={loadMore} disabled={loading}>
              {loading ? "Loading..." : "Load More Vendors"}
            </Button>
          </div>
        )}
      </div>
    </div>
  );
};
```

---

## 🔧 **ADVANCED TECHNICAL SPECIFICATIONS**

### **Search and Filtering System**

#### **Elasticsearch Integration (Optional)**
```typescript
// Advanced search capabilities
interface VendorSearchQuery {
  text?: string;                    // Full-text search
  location?: {
    lat: number;
    lng: number;
    radius: number;                 // km
  };
  ceremony_types?: CeremonyType[];
  business_types?: string[];
  price_range?: {
    min: number;
    max: number;
  };
  rating_min?: number;
  availability?: {
    start_date: Date;
    end_date: Date;
  };
  verified_only?: boolean;
  sort_by?: 'relevance' | 'rating' | 'price_low' | 'price_high' | 'distance';
}

// Search service implementation
class VendorSearchService {
  async searchVendors(query: VendorSearchQuery): Promise<{
    vendors: VendorProfile[];
    total: number;
    facets: SearchFacets;
  }> {
    // Implementation with Supabase full-text search or external search service
  }

  async getSearchSuggestions(partial: string): Promise<string[]> {
    // Auto-complete suggestions
  }

  async getPopularSearches(): Promise<string[]> {
    // Trending searches
  }
}
```

### **Performance Optimization Strategy**

#### **Image Optimization**
```typescript
// Vendor portfolio image handling
interface ImageOptimizationConfig {
  thumbnail: { width: 300, height: 200 };
  card: { width: 600, height: 400 };
  gallery: { width: 1200, height: 800 };
  hero: { width: 1920, height: 1080 };
}

// Lazy loading with intersection observer
const VendorPortfolioGallery: React.FC = ({ images }) => {
  const [visibleImages, setVisibleImages] = useState<Set<string>>(new Set());

  return (
    <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
      {images.map((image, index) => (
        <LazyImage
          key={image.id}
          src={image.url}
          alt={image.alt}
          className="aspect-square object-cover rounded-lg"
          placeholder="blur"
          onVisible={() => setVisibleImages(prev => new Set([...prev, image.id]))}
        />
      ))}
    </div>
  );
};
```

#### **Database Query Optimization**
```sql
-- Optimized vendor search with proper indexing
CREATE INDEX CONCURRENTLY idx_vendor_profiles_search
ON vendor_profiles USING GIN (
  to_tsvector('english', business_name || ' ' || description)
);

CREATE INDEX CONCURRENTLY idx_vendor_profiles_location
ON vendor_profiles USING GIST (location);

CREATE INDEX CONCURRENTLY idx_vendor_profiles_ceremony_types
ON vendor_profiles USING GIN (ceremony_types);

CREATE INDEX CONCURRENTLY idx_vendor_profiles_rating
ON vendor_profiles (rating_average DESC, rating_count DESC);

-- Optimized search query
CREATE OR REPLACE FUNCTION search_vendors(
  search_text TEXT DEFAULT NULL,
  ceremony_type_filter TEXT[] DEFAULT NULL,
  business_type_filter TEXT[] DEFAULT NULL,
  min_rating DECIMAL DEFAULT NULL,
  price_min DECIMAL DEFAULT NULL,
  price_max DECIMAL DEFAULT NULL,
  limit_count INTEGER DEFAULT 20,
  offset_count INTEGER DEFAULT 0
) RETURNS TABLE (
  vendor_profile vendor_profiles,
  relevance_score REAL
) AS $$
BEGIN
  RETURN QUERY
  SELECT
    vp.*,
    CASE
      WHEN search_text IS NOT NULL THEN
        ts_rank(to_tsvector('english', vp.business_name || ' ' || vp.description),
                plainto_tsquery('english', search_text))
      ELSE 1.0
    END as relevance_score
  FROM vendor_profiles vp
  WHERE
    (search_text IS NULL OR
     to_tsvector('english', vp.business_name || ' ' || vp.description) @@
     plainto_tsquery('english', search_text))
    AND (ceremony_type_filter IS NULL OR
         vp.ceremony_types && ceremony_type_filter)
    AND (business_type_filter IS NULL OR
         vp.business_type = ANY(business_type_filter))
    AND (min_rating IS NULL OR vp.rating_average >= min_rating)
    AND (price_min IS NULL OR vp.price_range_min >= price_min)
    AND (price_max IS NULL OR vp.price_range_max <= price_max)
  ORDER BY relevance_score DESC, vp.rating_average DESC
  LIMIT limit_count OFFSET offset_count;
END;
$$ LANGUAGE plpgsql;
```

---

## 📊 **ANALYTICS AND MONITORING**

### **Vendor Performance Metrics**
```typescript
interface VendorAnalytics {
  profile_views: number;
  contact_requests: number;
  budget_connections: number;
  booking_requests: number;
  conversion_rate: number;
  average_response_time: number; // hours
  customer_satisfaction: number; // 1-5 rating
}

// Analytics dashboard for vendors
const VendorAnalyticsDashboard: React.FC = () => {
  return (
    <div className="space-y-6">
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <MetricCard
          title="Profile Views"
          value={analytics.profile_views}
          change="+12%"
          period="vs last month"
        />
        <MetricCard
          title="Contact Requests"
          value={analytics.contact_requests}
          change="+8%"
          period="vs last month"
        />
        <MetricCard
          title="Conversion Rate"
          value={`${(analytics.conversion_rate * 100).toFixed(1)}%`}
          change="+2.3%"
          period="vs last month"
        />
        <MetricCard
          title="Avg Response Time"
          value={`${analytics.average_response_time}h`}
          change="-0.5h"
          period="vs last month"
        />
      </div>

      {/* Charts and detailed analytics */}
      <Card>
        <CardHeader>
          <CardTitle>Booking Trends</CardTitle>
        </CardHeader>
        <CardContent>
          <BookingTrendsChart data={bookingData} />
        </CardContent>
      </Card>
    </div>
  );
};
```

---

## 📋 **CONCLUSION & RECOMMENDATIONS**

### **Recommended Approach: Phased Implementation**
1. **Start with Phase 1**: Focus on core vendor management and budget integration
2. **User Testing**: Conduct extensive testing after Phase 1 before proceeding
3. **Iterative Development**: Gather user feedback and adjust Phase 2/3 accordingly
4. **Performance First**: Prioritize mobile performance and search optimization

### **Risk Mitigation Strategies**
1. **Feature Flags**: Use feature flags to gradually roll out vendor features
2. **A/B Testing**: Test vendor integration impact on core user flows
3. **Fallback Options**: Ensure core pledge/budget features remain unaffected
4. **User Education**: Provide clear onboarding for vendor features

### **Long-term Vision**
The service provider directory should enhance, not replace, the core PledgeForLove experience. Success will be measured by improved user satisfaction in wedding planning while maintaining the platform's simplicity and focus on digital pledge management.
