import React, { useState, useEffect, useCallback } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Progress } from '@/components/ui/progress';
import { Badge } from '@/components/ui/badge';
import { 
  DollarSign, 
  TrendingUp, 
  TrendingDown, 
  AlertCircle, 
  CheckCircle,
  Plus,
  Settings,
  Download
} from 'lucide-react';
import { useToast } from '@/hooks/use-toast';
import { budgetOperations } from '@/integrations/budget';
import { formatCurrency, getBudgetStatus } from '@/utils/budgetUtils';
import type { Budget, BudgetItem, BudgetSummary, CeremonyType } from '@/types/app';
import { getCeremonyDisplayName } from '@/utils/ceremonyBudgetCategories';

interface BudgetDashboardProps {
  userId: string;
  ceremonyType?: CeremonyType;
  onCreateBudget?: () => void;
  onManageItems?: () => void;
  onExportBudget?: () => void;
}

export const BudgetDashboard: React.FC<BudgetDashboardProps> = ({
  userId,
  ceremonyType = 'wedding',
  onCreateBudget,
  onManageItems,
  onExportBudget
}) => {
  const [budget, setBudget] = useState<Budget | null>(null);
  const [items, setItems] = useState<BudgetItem[]>([]);
  const [summary, setSummary] = useState<BudgetSummary | null>(null);
  const [loading, setLoading] = useState(true);
  const { toast } = useToast();

  useEffect(() => {
    loadBudgetData();
  }, [userId, ceremonyType]);

  const loadBudgetData = useCallback(async () => {
    try {
      setLoading(true);
      const data = await budgetOperations.getCompleteBudgetData(userId);
      setBudget(data.budget);
      setItems(data.items);
      setSummary(data.summary);
    } catch (error) {
      console.error('Error loading budget data:', error);
      toast({
        title: "Error",
        description: "Failed to load budget data. Please try again.",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  }, [userId, toast]);

  if (loading) {
    return (
      <div className="space-y-6">
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          {[1, 2, 3].map((i) => (
            <Card key={i} className="animate-pulse">
              <CardHeader className="pb-3">
                <div className="h-4 bg-gray-200 rounded w-1/2"></div>
              </CardHeader>
              <CardContent>
                <div className="h-8 bg-gray-200 rounded w-3/4"></div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    );
  }

  if (!budget || !summary) {
    return (
      <Card>
        <CardHeader className="text-center">
          <CardTitle className="flex items-center justify-center gap-2">
            <DollarSign className="h-6 w-6" />
            No Budget Set
          </CardTitle>
          <CardDescription>
            Create a budget for your {getCeremonyDisplayName(ceremonyType).toLowerCase()} to start tracking expenses
          </CardDescription>
        </CardHeader>
        <CardContent className="text-center">
          <Button onClick={onCreateBudget} className="w-full sm:w-auto">
            <Plus className="h-4 w-4 mr-2" />
            Create Budget
          </Button>
        </CardContent>
      </Card>
    );
  }

  const budgetStatus = getBudgetStatus(summary);
  const progressPercentage = budget.total_budget > 0 
    ? Math.min((summary.total_spent / budget.total_budget) * 100, 100)
    : 0;

  return (
    <div className="space-y-6">
      {/* Budget Overview Cards */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="text-sm font-medium flex items-center gap-2">
              <DollarSign className="h-4 w-4" />
              Total Budget
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {formatCurrency(budget.total_budget, budget.currency)}
            </div>
            <p className="text-xs text-muted-foreground mt-1">
              {getCeremonyDisplayName(ceremonyType)} Budget
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="text-sm font-medium flex items-center gap-2">
              <TrendingUp className="h-4 w-4" />
              Total Spent
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {formatCurrency(summary.total_spent, budget.currency)}
            </div>
            <p className="text-xs text-muted-foreground mt-1">
              {progressPercentage.toFixed(1)}% of budget
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="text-sm font-medium flex items-center gap-2">
              <TrendingDown className="h-4 w-4" />
              Remaining
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {formatCurrency(summary.remaining_budget, budget.currency)}
            </div>
            <div className="flex items-center gap-2 mt-1">
              <Badge 
                variant={budgetStatus.status === 'over_budget' ? 'destructive' : 'secondary'}
                className="text-xs"
              >
                {budgetStatus.message}
              </Badge>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Budget Progress */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center justify-between">
            <span>Budget Progress</span>
            <div className="flex gap-2">
              <Button variant="outline" size="sm" onClick={onManageItems}>
                <Settings className="h-4 w-4 mr-2" />
                Manage Items
              </Button>
              <Button variant="outline" size="sm" onClick={onExportBudget}>
                <Download className="h-4 w-4 mr-2" />
                Export
              </Button>
            </div>
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="space-y-2">
            <div className="flex justify-between text-sm">
              <span>Budget Utilization</span>
              <span>{progressPercentage.toFixed(1)}%</span>
            </div>
            <Progress
              value={progressPercentage}
              className={`h-2 ${progressPercentage > 100 ? 'bg-red-100' : ''}`}
            />
          </div>

          {/* Completion Statistics */}
          <div className="space-y-2">
            <div className="flex justify-between text-sm">
              <span>Items Completed</span>
              <span>{items.filter(item => item.is_completed).length} of {items.length}</span>
            </div>
            <Progress
              value={items.length > 0 ? (items.filter(item => item.is_completed).length / items.length) * 100 : 0}
              className="h-2"
            />
          </div>

          {progressPercentage > 90 && (
            <div className="flex items-center gap-2 p-3 bg-yellow-50 border border-yellow-200 rounded-lg">
              <AlertCircle className="h-4 w-4 text-yellow-600" />
              <span className="text-sm text-yellow-800">
                {progressPercentage > 100 
                  ? 'You have exceeded your budget!' 
                  : 'You are approaching your budget limit.'}
              </span>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Top Categories */}
      <Card>
        <CardHeader>
          <CardTitle>Top Expense Categories</CardTitle>
          <CardDescription>
            Your highest spending categories for this {getCeremonyDisplayName(ceremonyType).toLowerCase()}
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-3">
            {items
              .filter(item => item.actual_cost && item.actual_cost > 0)
              .sort((a, b) => (b.actual_cost || 0) - (a.actual_cost || 0))
              .slice(0, 5)
              .map((item, index) => (
                <div key={item.id} className="flex items-center justify-between">
                  <div className="flex items-center gap-3">
                    <div className="w-8 h-8 rounded-full bg-primary/10 flex items-center justify-center">
                      <span className="text-sm font-medium">{index + 1}</span>
                    </div>
                    <div>
                      <p className="font-medium">{item.category}</p>
                      <p className="text-sm text-muted-foreground">
                        Estimated: {formatCurrency(item.estimated_cost, budget.currency)}
                      </p>
                    </div>
                  </div>
                  <div className="text-right">
                    <p className="font-medium">
                      {formatCurrency(item.actual_cost || 0, budget.currency)}
                    </p>
                    <div className="flex items-center gap-1">
                      {(item.actual_cost || 0) <= item.estimated_cost ? (
                        <CheckCircle className="h-3 w-3 text-green-500" />
                      ) : (
                        <AlertCircle className="h-3 w-3 text-red-500" />
                      )}
                      <span className="text-xs text-muted-foreground">
                        {item.is_completed ? 'Complete' : 'In Progress'}
                      </span>
                    </div>
                  </div>
                </div>
              ))}
            
            {items.filter(item => item.actual_cost && item.actual_cost > 0).length === 0 && (
              <div className="text-center py-6 text-muted-foreground">
                <p>No expenses recorded yet</p>
                <p className="text-sm">Start adding actual costs to see your top categories</p>
              </div>
            )}
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default BudgetDashboard;
