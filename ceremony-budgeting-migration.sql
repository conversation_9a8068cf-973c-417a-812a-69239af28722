-- =====================================================
-- CEREMONY BUDGETING FEATURE MIGRATION
-- =====================================================
-- This migration creates the complete budgeting system
-- with ceremony type support and cultural customization
--
-- Run this manually in your Supabase SQL editor
-- =====================================================

-- 1. Create budgets table (if it doesn't exist)
-- =====================================================
CREATE TABLE IF NOT EXISTS budgets (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    total_budget DECIMAL(12,2) NOT NULL DEFAULT 0,
    currency TEXT NOT NULL DEFAULT 'UGX',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),

    -- Ensure one budget per user
    UNIQUE(user_id)
);

-- Enable RLS on budgets table
ALTER TABLE budgets ENABLE ROW LEVEL SECURITY;

-- Create RLS policies for budgets
CREATE POLICY "budgets_select_policy" ON budgets
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "budgets_insert_policy" ON budgets
    FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "budgets_update_policy" ON budgets
    FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "budgets_delete_policy" ON budgets
    FOR DELETE USING (auth.uid() = user_id);

-- 2. Create budget_items table (if it doesn't exist)
-- =====================================================
CREATE TABLE IF NOT EXISTS budget_items (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    budget_id UUID NOT NULL REFERENCES budgets(id) ON DELETE CASCADE,
    category TEXT NOT NULL,
    estimated_cost DECIMAL(12,2) NOT NULL DEFAULT 0,
    actual_cost DECIMAL(12,2),
    notes TEXT,
    is_completed BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Enable RLS on budget_items table
ALTER TABLE budget_items ENABLE ROW LEVEL SECURITY;

-- Create RLS policies for budget_items
CREATE POLICY "budget_items_select_policy" ON budget_items
    FOR SELECT USING (
        budget_id IN (
            SELECT id FROM budgets WHERE user_id = auth.uid()
        )
    );

CREATE POLICY "budget_items_insert_policy" ON budget_items
    FOR INSERT WITH CHECK (
        budget_id IN (
            SELECT id FROM budgets WHERE user_id = auth.uid()
        )
    );

CREATE POLICY "budget_items_update_policy" ON budget_items
    FOR UPDATE USING (
        budget_id IN (
            SELECT id FROM budgets WHERE user_id = auth.uid()
        )
    );

CREATE POLICY "budget_items_delete_policy" ON budget_items
    FOR DELETE USING (
        budget_id IN (
            SELECT id FROM budgets WHERE user_id = auth.uid()
        )
    );

-- 3. Create budget summary function (if it doesn't exist)
-- =====================================================
CREATE OR REPLACE FUNCTION get_budget_summary(p_user_id UUID)
RETURNS TABLE (
    total_budget DECIMAL(12,2),
    total_estimated DECIMAL(12,2),
    total_actual DECIMAL(12,2),
    total_spent DECIMAL(12,2),
    remaining_budget DECIMAL(12,2)
)
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
    RETURN QUERY
    SELECT
        COALESCE(b.total_budget, 0) as total_budget,
        COALESCE(SUM(bi.estimated_cost), 0) as total_estimated,
        COALESCE(SUM(bi.actual_cost), 0) as total_actual,
        COALESCE(SUM(bi.actual_cost), 0) as total_spent,
        COALESCE(b.total_budget, 0) - COALESCE(SUM(bi.actual_cost), 0) as remaining_budget
    FROM budgets b
    LEFT JOIN budget_items bi ON b.id = bi.budget_id
    WHERE b.user_id = p_user_id
    GROUP BY b.id, b.total_budget;
END;
$$;

-- Grant execute permission on budget summary function
GRANT EXECUTE ON FUNCTION get_budget_summary(UUID) TO authenticated;

-- 4. Add ceremony_type column to profiles table
-- =====================================================
DO $$ 
BEGIN
    -- Check if ceremony_type column exists
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'profiles' 
        AND column_name = 'ceremony_type'
    ) THEN
        ALTER TABLE profiles 
        ADD COLUMN ceremony_type TEXT DEFAULT 'wedding' 
        CHECK (ceremony_type IN ('wedding', 'kukyala', 'nikah', 'kuhingira'));
        
        -- Update existing records to have default ceremony type
        UPDATE profiles SET ceremony_type = 'wedding' WHERE ceremony_type IS NULL;
        
        RAISE NOTICE 'Added ceremony_type column to profiles table';
    ELSE
        RAISE NOTICE 'ceremony_type column already exists in profiles table';
    END IF;
END $$;

-- 5. Add ceremony_date column to profiles table
-- =====================================================
DO $$ 
BEGIN
    -- Check if ceremony_date column exists
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'profiles' 
        AND column_name = 'ceremony_date'
    ) THEN
        ALTER TABLE profiles 
        ADD COLUMN ceremony_date DATE;
        
        -- Copy wedding_date to ceremony_date for existing records
        UPDATE profiles 
        SET ceremony_date = wedding_date::DATE 
        WHERE wedding_date IS NOT NULL AND ceremony_date IS NULL;
        
        RAISE NOTICE 'Added ceremony_date column to profiles table';
    ELSE
        RAISE NOTICE 'ceremony_date column already exists in profiles table';
    END IF;
END $$;

-- 6. Add ceremony_type column to budget_items table
-- =====================================================
DO $$ 
BEGIN
    -- Check if ceremony_type column exists in budget_items
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'budget_items' 
        AND column_name = 'ceremony_type'
    ) THEN
        ALTER TABLE budget_items 
        ADD COLUMN ceremony_type TEXT DEFAULT 'wedding'
        CHECK (ceremony_type IN ('wedding', 'kukyala', 'nikah', 'kuhingira'));
        
        -- Update existing budget items to have default ceremony type
        UPDATE budget_items SET ceremony_type = 'wedding' WHERE ceremony_type IS NULL;
        
        RAISE NOTICE 'Added ceremony_type column to budget_items table';
    ELSE
        RAISE NOTICE 'ceremony_type column already exists in budget_items table';
    END IF;
END $$;

-- 7. Create ceremony_budget_templates table
-- =====================================================
CREATE TABLE IF NOT EXISTS ceremony_budget_templates (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    ceremony_type TEXT NOT NULL CHECK (ceremony_type IN ('wedding', 'kukyala', 'nikah', 'kuhingira')),
    category TEXT NOT NULL,
    typical_percentage DECIMAL(5,2), -- % of total budget
    is_essential BOOLEAN DEFAULT FALSE,
    cultural_notes TEXT,
    display_order INTEGER DEFAULT 0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- Ensure unique category per ceremony type
    UNIQUE(ceremony_type, category)
);

-- 8. Insert default ceremony budget templates
-- =====================================================
INSERT INTO ceremony_budget_templates (ceremony_type, category, typical_percentage, is_essential, cultural_notes, display_order)
VALUES 
-- WEDDING CATEGORIES
('wedding', 'Venue & Catering', 40.0, true, 'Often the largest expense for modern weddings', 1),
('wedding', 'Photography & Videography', 10.0, true, 'Captures memories for a lifetime', 2),
('wedding', 'Attire & Accessories', 8.0, true, 'Traditional and modern wedding attire', 3),
('wedding', 'Decoration & Flowers', 8.0, true, 'Creates the wedding atmosphere', 4),
('wedding', 'Music & Entertainment', 8.0, true, 'Sets the mood for celebration', 5),
('wedding', 'Transportation', 3.0, false, 'Special transport for the couple', 6),
('wedding', 'Wedding Rings', 3.0, true, 'Symbol of eternal commitment', 7),
('wedding', 'Beauty & Grooming', 5.0, true, 'Looking perfect for the special day', 8),
('wedding', 'Wedding Cake', 2.0, true, 'Traditional cake cutting ceremony', 9),
('wedding', 'Stationery & Invitations', 2.0, true, 'Formal invitations to guests', 10),
('wedding', 'Legal & Documentation', 1.0, true, 'Legal requirements for marriage', 11),
('wedding', 'Gifts & Favors', 3.0, false, 'Thank you gifts for guests', 12),
('wedding', 'Contingency', 5.0, true, 'Buffer for unexpected costs', 13),
('wedding', 'Other', 2.0, false, 'Additional expenses not covered above', 14),

-- KUKYALA CATEGORIES
('kukyala', 'Traditional Attire', 20.0, true, 'Proper traditional dress shows respect', 1),
('kukyala', 'Gifts for Bride''s Family', 30.0, true, 'Shows appreciation and respect to bride''s family', 2),
('kukyala', 'Traditional Drinks', 15.0, true, 'Essential for traditional ceremonies', 3),
('kukyala', 'Cultural Decorations', 8.0, true, 'Creates authentic cultural atmosphere', 4),
('kukyala', 'Traditional Music', 10.0, true, 'Traditional songs and dances', 5),
('kukyala', 'Transportation', 5.0, false, 'Ensuring all family members can attend', 6),
('kukyala', 'Photography', 5.0, false, 'Preserving cultural memories', 7),
('kukyala', 'Ceremony Venue', 3.0, true, 'Appropriate setting for the ceremony', 8),
('kukyala', 'Traditional Food', 2.0, true, 'Sharing traditional meals', 9),
('kukyala', 'Cultural Advisor', 2.0, false, 'Guidance on proper cultural protocols', 10),

-- NIKAH CATEGORIES
('nikah', 'Mahr (Dower)', 25.0, true, 'Islamic requirement, amount agreed by both parties', 1),
('nikah', 'Mosque/Venue Fees', 5.0, true, 'Sacred space for the Islamic ceremony', 2),
('nikah', 'Islamic Attire', 15.0, true, 'Modest and appropriate Islamic dress', 3),
('nikah', 'Walima Preparation', 30.0, true, 'Sunnah to celebrate with community', 4),
('nikah', 'Religious Decorations', 5.0, false, 'Beautiful but modest decorations', 5),
('nikah', 'Halal Catering', 10.0, true, 'All food must be halal certified', 6),
('nikah', 'Islamic Music/Nasheed', 3.0, false, 'Islamic songs and nasheeds only', 7),
('nikah', 'Transportation', 3.0, false, 'Ensuring community can attend', 8),
('nikah', 'Photography', 2.0, false, 'Respectful documentation of the ceremony', 9),
('nikah', 'Religious Documentation', 2.0, true, 'Official Islamic marriage documentation', 10),

-- KUHINGIRA CATEGORIES
('kuhingira', 'Traditional Ceremony Venue', 15.0, true, 'Appropriate traditional setting', 1),
('kuhingira', 'Cultural Attire', 20.0, true, 'Authentic traditional dress for all participants', 2),
('kuhingira', 'Traditional Gifts', 25.0, true, 'Gifts that honor cultural traditions', 3),
('kuhingira', 'Cultural Decorations', 10.0, true, 'Authentic cultural atmosphere', 4),
('kuhingira', 'Traditional Entertainment', 12.0, true, 'Traditional songs and cultural performances', 5),
('kuhingira', 'Ceremonial Items', 8.0, true, 'Sacred items for traditional rituals', 6),
('kuhingira', 'Photography & Documentation', 3.0, false, 'Preserving cultural heritage', 7),
('kuhingira', 'Traditional Food', 5.0, true, 'Traditional foods for the ceremony', 8),
('kuhingira', 'Transportation', 1.0, false, 'Ensuring all can participate', 9),
('kuhingira', 'Cultural Guide', 1.0, false, 'Proper execution of cultural protocols', 10)

ON CONFLICT (ceremony_type, category) DO NOTHING;

-- 9. Create indexes for better performance
-- =====================================================
-- Indexes for budget tables
CREATE INDEX IF NOT EXISTS idx_budgets_user_id ON budgets(user_id);
CREATE INDEX IF NOT EXISTS idx_budget_items_budget_id ON budget_items(budget_id);
CREATE INDEX IF NOT EXISTS idx_budget_items_category ON budget_items(category);

-- Indexes for ceremony features
CREATE INDEX IF NOT EXISTS idx_profiles_ceremony_type ON profiles(ceremony_type);
CREATE INDEX IF NOT EXISTS idx_budget_items_ceremony_type ON budget_items(ceremony_type);
CREATE INDEX IF NOT EXISTS idx_ceremony_budget_templates_type ON ceremony_budget_templates(ceremony_type);
CREATE INDEX IF NOT EXISTS idx_ceremony_budget_templates_order ON ceremony_budget_templates(ceremony_type, display_order);

-- 10. Update RLS policies for new tables
-- =====================================================
-- Enable RLS on ceremony_budget_templates table
ALTER TABLE ceremony_budget_templates ENABLE ROW LEVEL SECURITY;

-- Create policy for ceremony_budget_templates (read-only for all authenticated users)
CREATE POLICY "ceremony_budget_templates_select_policy" ON ceremony_budget_templates
    FOR SELECT USING (auth.role() = 'authenticated');

-- 11. Create helper functions
-- =====================================================

-- Function to get ceremony-specific budget categories
CREATE OR REPLACE FUNCTION get_ceremony_budget_categories(p_ceremony_type TEXT)
RETURNS TABLE (
    category TEXT,
    typical_percentage DECIMAL(5,2),
    is_essential BOOLEAN,
    cultural_notes TEXT,
    display_order INTEGER
) 
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
    RETURN QUERY
    SELECT 
        cbt.category,
        cbt.typical_percentage,
        cbt.is_essential,
        cbt.cultural_notes,
        cbt.display_order
    FROM ceremony_budget_templates cbt
    WHERE cbt.ceremony_type = p_ceremony_type
    ORDER BY cbt.display_order, cbt.category;
END;
$$;

-- Function to initialize ceremony-specific budget
CREATE OR REPLACE FUNCTION initialize_ceremony_budget(
    p_user_id UUID,
    p_ceremony_type TEXT,
    p_total_budget DECIMAL(12,2),
    p_currency TEXT DEFAULT 'UGX'
)
RETURNS TABLE (
    budget_id UUID,
    items_created INTEGER
)
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    v_budget_id UUID;
    v_items_created INTEGER := 0;
    v_category RECORD;
    v_estimated_cost DECIMAL(12,2);
BEGIN
    -- Create or update budget
    INSERT INTO budgets (user_id, total_budget, currency)
    VALUES (p_user_id, p_total_budget, p_currency)
    ON CONFLICT (user_id) 
    DO UPDATE SET 
        total_budget = EXCLUDED.total_budget,
        currency = EXCLUDED.currency,
        updated_at = NOW()
    RETURNING id INTO v_budget_id;
    
    -- Delete existing budget items for this user to avoid duplicates
    DELETE FROM budget_items WHERE budget_id = v_budget_id;
    
    -- Create budget items based on ceremony type
    FOR v_category IN 
        SELECT * FROM get_ceremony_budget_categories(p_ceremony_type)
    LOOP
        -- Calculate estimated cost based on percentage
        v_estimated_cost := CASE 
            WHEN v_category.typical_percentage IS NOT NULL 
            THEN ROUND((p_total_budget * v_category.typical_percentage / 100), 2)
            ELSE 0
        END;
        
        -- Insert budget item
        INSERT INTO budget_items (
            budget_id,
            category,
            estimated_cost,
            notes,
            ceremony_type
        ) VALUES (
            v_budget_id,
            v_category.category,
            v_estimated_cost,
            v_category.cultural_notes,
            p_ceremony_type
        );
        
        v_items_created := v_items_created + 1;
    END LOOP;
    
    RETURN QUERY SELECT v_budget_id, v_items_created;
END;
$$;

-- 12. Update existing budget summary function to include ceremony context
-- =====================================================
CREATE OR REPLACE FUNCTION get_budget_summary_with_ceremony(p_user_id UUID)
RETURNS TABLE (
    total_budget DECIMAL(12,2),
    total_estimated DECIMAL(12,2),
    total_actual DECIMAL(12,2),
    total_spent DECIMAL(12,2),
    remaining_budget DECIMAL(12,2),
    ceremony_type TEXT,
    currency TEXT
)
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
    RETURN QUERY
    SELECT 
        COALESCE(b.total_budget, 0) as total_budget,
        COALESCE(SUM(bi.estimated_cost), 0) as total_estimated,
        COALESCE(SUM(bi.actual_cost), 0) as total_actual,
        COALESCE(SUM(bi.actual_cost), 0) as total_spent,
        COALESCE(b.total_budget, 0) - COALESCE(SUM(bi.actual_cost), 0) as remaining_budget,
        COALESCE(p.ceremony_type, 'wedding') as ceremony_type,
        COALESCE(b.currency, 'UGX') as currency
    FROM budgets b
    LEFT JOIN budget_items bi ON b.id = bi.budget_id
    LEFT JOIN profiles p ON b.user_id = p.id
    WHERE b.user_id = p_user_id
    GROUP BY b.id, b.total_budget, b.currency, p.ceremony_type;
END;
$$;

-- 13. Grant necessary permissions
-- =====================================================
-- Grant permissions on budget tables
GRANT SELECT, INSERT, UPDATE, DELETE ON budgets TO authenticated;
GRANT SELECT, INSERT, UPDATE, DELETE ON budget_items TO authenticated;

-- Grant permissions on ceremony_budget_templates
GRANT SELECT ON ceremony_budget_templates TO authenticated;

-- Grant execute permissions on functions
GRANT EXECUTE ON FUNCTION get_ceremony_budget_categories(TEXT) TO authenticated;
GRANT EXECUTE ON FUNCTION initialize_ceremony_budget(UUID, TEXT, DECIMAL, TEXT) TO authenticated;
GRANT EXECUTE ON FUNCTION get_budget_summary_with_ceremony(UUID) TO authenticated;

-- 14. Add comments for documentation
-- =====================================================
-- Budget table comments
COMMENT ON TABLE budgets IS 'User budget information with total budget and currency';
COMMENT ON TABLE budget_items IS 'Individual budget line items with estimated and actual costs';
COMMENT ON COLUMN budgets.user_id IS 'Reference to the user who owns this budget';
COMMENT ON COLUMN budgets.total_budget IS 'Total budget amount for the ceremony';
COMMENT ON COLUMN budgets.currency IS 'Currency code (UGX, USD, EUR, etc.)';
COMMENT ON COLUMN budget_items.budget_id IS 'Reference to the parent budget';
COMMENT ON COLUMN budget_items.category IS 'Budget category name (e.g., Venue & Catering)';
COMMENT ON COLUMN budget_items.estimated_cost IS 'Estimated cost for this category';
COMMENT ON COLUMN budget_items.actual_cost IS 'Actual cost spent on this category';
COMMENT ON COLUMN budget_items.is_completed IS 'Whether this budget item is completed';

-- Ceremony feature comments
COMMENT ON COLUMN profiles.ceremony_type IS 'Type of ceremony: wedding, kukyala, nikah, or kuhingira';
COMMENT ON COLUMN profiles.ceremony_date IS 'Date of the ceremony (can be different from wedding_date for flexibility)';
COMMENT ON COLUMN budget_items.ceremony_type IS 'Ceremony type context for this budget item';
COMMENT ON TABLE ceremony_budget_templates IS 'Template categories and guidance for different ceremony types';

-- Function comments
COMMENT ON FUNCTION get_budget_summary(UUID) IS 'Returns budget summary with totals and remaining amount';
COMMENT ON FUNCTION get_ceremony_budget_categories(TEXT) IS 'Returns budget categories specific to a ceremony type';
COMMENT ON FUNCTION initialize_ceremony_budget(UUID, TEXT, DECIMAL, TEXT) IS 'Creates a budget with ceremony-specific categories';
COMMENT ON FUNCTION get_budget_summary_with_ceremony(UUID) IS 'Returns budget summary with ceremony context';

-- =====================================================
-- MIGRATION COMPLETE
-- =====================================================
-- 
-- This migration creates:
-- 1. Complete budgeting system (budgets and budget_items tables)
-- 2. Row Level Security (RLS) policies for data protection
-- 3. Ceremony type support to profiles
-- 4. Ceremony date field for flexibility
-- 5. Ceremony context to budget items
-- 6. Template system for ceremony-specific categories
-- 7. Helper functions for ceremony budget management
-- 8. Proper indexing for performance
-- 9. Comprehensive permissions and documentation
--
-- After running this migration, the application will support:
-- - Wedding (Modern/Church ceremonies)
-- - Kukyala (Traditional introduction ceremonies)
-- - Nikah (Islamic wedding ceremonies)  
-- - Kuhingira (Traditional cultural ceremonies)
--
-- Each ceremony type has culturally appropriate budget
-- categories with guidance and typical percentages.
-- =====================================================

-- Final verification query (optional - run to verify migration)
-- SELECT 'Migration completed successfully. Ceremony types available:' as status;
-- SELECT ceremony_type, COUNT(*) as categories_count 
-- FROM ceremony_budget_templates 
-- GROUP BY ceremony_type 
-- ORDER BY ceremony_type;
