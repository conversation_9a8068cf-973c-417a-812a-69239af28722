import { describe, it, expect, vi, beforeEach } from 'vitest';
import { render, screen, fireEvent } from '@testing-library/react';
import { BrowserRouter } from 'react-router-dom';
import MobileNavigation from '../MobileNavigation';

type MockFunction = ReturnType<typeof vi.fn>;

// Mock react-router-dom
const mockNavigate = vi.fn();
vi.mock('react-router-dom', async () => {
  const actual = await vi.importActual('react-router-dom');
  return {
    ...actual,
    useNavigate: () => mockNavigate,
  };
});

// Mock AuthModal context
const mockOpenAuthModal = vi.fn();
vi.mock('@/contexts/AuthModalContext', () => ({
  useAuthModal: () => ({
    openAuthModal: mockOpenAuthModal,
    closeAuthModal: vi.fn(),
    isAuthModalOpen: false,
    authModalMode: 'signin',
  }),
}));

// Mock Auth context
const mockUser = { id: 'test-user-id', email: '<EMAIL>' };
const mockProfile = {
  treasurer_name: '<PERSON>',
  bride_name: '<PERSON>',
  groom_name: 'John'
};

vi.mock('@/contexts/AuthContext', () => ({
  useAuth: () => ({
    user: mockUser,
    profile: mockProfile,
    signOut: vi.fn(),
  }),
}));

// Mock toast
vi.mock('@/hooks/use-toast', () => ({
  useToast: () => ({
    toast: vi.fn(),
  }),
}));

const renderMobileNavigation = (props = {}) => {
  const defaultProps = {
    isAdmin: false,
    isSigningOut: false,
    onSignOut: vi.fn(),
    ...props,
  };

  return render(
    <BrowserRouter>
      <MobileNavigation {...defaultProps} />
    </BrowserRouter>
  );
};

describe('MobileNavigation Component', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('renders the hamburger menu button', () => {
    renderMobileNavigation();
    
    const menuButton = screen.getByRole('button', { name: /open navigation menu/i });
    expect(menuButton).toBeInTheDocument();
  });

  it('opens the navigation sheet when hamburger button is clicked', () => {
    renderMobileNavigation();
    
    const menuButton = screen.getByRole('button', { name: /open navigation menu/i });
    fireEvent.click(menuButton);
    
    // Check if the sheet content is visible
    expect(screen.getByText('PledgeForLove')).toBeInTheDocument();
  });

  it('displays user information when authenticated', () => {
    renderMobileNavigation();
    
    const menuButton = screen.getByRole('button', { name: /open navigation menu/i });
    fireEvent.click(menuButton);
    
    expect(screen.getByText('Welcome, John Doe')).toBeInTheDocument();
    expect(screen.getByText('Jane & John')).toBeInTheDocument();
  });

  it('shows regular user navigation items when not admin', () => {
    renderMobileNavigation({ isAdmin: false });
    
    const menuButton = screen.getByRole('button', { name: /open navigation menu/i });
    fireEvent.click(menuButton);
    
    expect(screen.getByText('Dashboard')).toBeInTheDocument();
    expect(screen.getByText('Profile')).toBeInTheDocument();
    expect(screen.getByText('FAQ')).toBeInTheDocument();
    expect(screen.getByText('View Demo')).toBeInTheDocument();
    expect(screen.getByText('Sign Out')).toBeInTheDocument();
  });

  it('shows admin navigation items when user is admin', () => {
    renderMobileNavigation({ isAdmin: true });
    
    const menuButton = screen.getByRole('button', { name: /open navigation menu/i });
    fireEvent.click(menuButton);
    
    expect(screen.getByText('Admin Panel')).toBeInTheDocument();
    expect(screen.getByText('System Administrator')).toBeInTheDocument();
    // Should not show Dashboard and Profile for admin
    expect(screen.queryByText('Dashboard')).not.toBeInTheDocument();
    expect(screen.queryByText('Profile')).not.toBeInTheDocument();
  });

  it('calls onSignOut when sign out button is clicked', () => {
    const mockOnSignOut = vi.fn();
    renderMobileNavigation({ onSignOut: mockOnSignOut });
    
    const menuButton = screen.getByRole('button', { name: /open navigation menu/i });
    fireEvent.click(menuButton);
    
    const signOutButton = screen.getByText('Sign Out');
    fireEvent.click(signOutButton);
    
    expect(mockOnSignOut).toHaveBeenCalledTimes(1);
  });

  it('shows signing out state when isSigningOut is true', () => {
    renderMobileNavigation({ isSigningOut: true });
    
    const menuButton = screen.getByRole('button', { name: /open navigation menu/i });
    fireEvent.click(menuButton);
    
    expect(screen.getByText('Signing Out...')).toBeInTheDocument();
  });

  it('is hidden on desktop screens (md and above)', () => {
    renderMobileNavigation();
    
    const menuButton = screen.getByRole('button', { name: /open navigation menu/i });
    expect(menuButton).toHaveClass('md:hidden');
  });

  it('has proper touch target size for mobile', () => {
    renderMobileNavigation();
    
    const menuButton = screen.getByRole('button', { name: /open navigation menu/i });
    expect(menuButton).toHaveClass('h-11', 'w-11');
  });
});
