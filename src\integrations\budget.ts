import { supabase } from './supabase/client';
import type {
  Budget,
  BudgetInsert,
  BudgetUpdate,
  BudgetItem,
  BudgetItemInsert,
  BudgetItemUpdate,
  BudgetSummary,
  CeremonyType,
  BudgetWithCeremony,
  BudgetItemWithCeremony
} from '@/types/app';
import { handleSupabaseError } from '@/types/app';
import { validateBudgetAmount, validateBudgetItem } from '@/utils/budgetUtils';
import { getCeremonyBudgetTemplate } from '@/utils/ceremonyBudgetCategories';

// Budget CRUD operations
export const budgetService = {
  // Get user's budget
  async getBudget(userId: string): Promise<Budget | null> {
    try {
      const { data, error } = await supabase
        .from('wedding_budgets')
        .select('*')
        .eq('user_id', userId)
        .single();

      if (error) {
        if (error.code === 'PGRST116') {
          // No budget found
          return null;
        }
        throw error;
      }

      return data;
    } catch (error) {
      handleSupabaseError(error);
    }
  },

  // Create or update budget
  async upsertBudget(userId: string, budgetData: Omit<BudgetInsert, 'user_id'>): Promise<Budget> {
    try {
      // Validate budget amount
      const validation = validateBudgetAmount(budgetData.total_budget);
      if (!validation.isValid) {
        throw new Error(validation.errors.join(', '));
      }

      const { data, error } = await supabase
        .from('wedding_budgets')
        .upsert({
          user_id: userId,
          ...budgetData
        })
        .select()
        .single();

      if (error) throw error;
      return data;
    } catch (error) {
      handleSupabaseError(error);
    }
  },

  // Update budget
  async updateBudget(userId: string, budgetId: string, updates: BudgetUpdate): Promise<Budget> {
    try {
      // Validate budget amount if provided
      if (updates.total_budget !== undefined) {
        const validation = validateBudgetAmount(updates.total_budget);
        if (!validation.isValid) {
          throw new Error(validation.errors.join(', '));
        }
      }

      const { data, error } = await supabase
        .from('wedding_budgets')
        .update(updates)
        .eq('id', budgetId)
        .eq('user_id', userId) // Ensure user owns the budget
        .select()
        .single();

      if (error) throw error;
      return data;
    } catch (error) {
      handleSupabaseError(error);
    }
  },

  // Delete budget (will cascade delete all budget items)
  async deleteBudget(userId: string, budgetId: string): Promise<void> {
    try {
      const { error } = await supabase
        .from('wedding_budgets')
        .delete()
        .eq('id', budgetId)
        .eq('user_id', userId); // Ensure user owns the budget

      if (error) throw error;
    } catch (error) {
      handleSupabaseError(error);
    }
  },

  // Get budget summary
  async getBudgetSummary(userId: string): Promise<BudgetSummary | null> {
    try {
      const { data, error } = await supabase
        .rpc('get_budget_summary', { p_user_id: userId });

      if (error) throw error;
      return data?.[0] || null;
    } catch (error) {
      handleSupabaseError(error);
    }
  }
};

// Budget Items CRUD operations
export const budgetItemService = {
  // Get all budget items for a user
  async getBudgetItems(userId: string): Promise<BudgetItem[]> {
    try {
      // First get the user's budget
      const budget = await budgetService.getBudget(userId);
      if (!budget) {
        return [];
      }

      // Then get the budget items for that budget
      const { data, error } = await supabase
        .from('wedding_budget_items')
        .select('*')
        .eq('budget_id', budget.id)
        .order('created_at', { ascending: false });

      if (error) throw error;
      return data || [];
    } catch (error) {
      handleSupabaseError(error);
    }
  },

  // Get budget items by budget ID
  async getBudgetItemsByBudgetId(budgetId: string): Promise<BudgetItem[]> {
    try {
      const { data, error } = await supabase
        .from('wedding_budget_items')
        .select('*')
        .eq('budget_id', budgetId)
        .order('created_at', { ascending: false });

      if (error) throw error;
      return data || [];
    } catch (error) {
      handleSupabaseError(error);
    }
  },

  // Create budget item
  async createBudgetItem(userId: string, itemData: Omit<BudgetItemInsert, 'id' | 'created_at' | 'updated_at'>): Promise<BudgetItem> {
    try {
      // Validate budget item
      const validation = validateBudgetItem(itemData);
      if (!validation.isValid) {
        throw new Error(validation.errors.join(', '));
      }

      // Verify user owns the budget
      const budget = await budgetService.getBudget(userId);
      if (!budget || budget.id !== itemData.budget_id) {
        throw new Error('Budget not found or access denied');
      }

      const { data, error } = await supabase
        .from('wedding_budget_items')
        .insert(itemData)
        .select()
        .single();

      if (error) throw error;
      return data;
    } catch (error) {
      handleSupabaseError(error);
    }
  },

  // Update budget item
  async updateBudgetItem(userId: string, itemId: string, updates: BudgetItemUpdate): Promise<BudgetItem> {
    try {
      // Validate budget item updates (pass true for isUpdate flag)
      const validation = validateBudgetItem(updates, true);
      if (!validation.isValid) {
        throw new Error(validation.errors.join(', '));
      }

      // First verify the user owns this budget item
      const existingItem = await budgetItemService.getBudgetItem(userId, itemId);
      if (!existingItem) {
        throw new Error('Budget item not found or access denied');
      }

      const { data, error } = await supabase
        .from('wedding_budget_items')
        .update(updates)
        .eq('id', itemId)
        .select()
        .single();

      if (error) throw error;
      return data;
    } catch (error) {
      handleSupabaseError(error);
    }
  },

  // Delete budget item
  async deleteBudgetItem(userId: string, itemId: string): Promise<void> {
    try {
      // First verify the user owns this budget item
      const existingItem = await budgetItemService.getBudgetItem(userId, itemId);
      if (!existingItem) {
        throw new Error('Budget item not found or access denied');
      }

      const { error } = await supabase
        .from('wedding_budget_items')
        .delete()
        .eq('id', itemId);

      if (error) throw error;
    } catch (error) {
      handleSupabaseError(error);
    }
  },

  // Get budget item by ID
  async getBudgetItem(userId: string, itemId: string): Promise<BudgetItem | null> {
    try {
      // First get the user's budget
      const budget = await budgetService.getBudget(userId);
      if (!budget) {
        return null;
      }

      const { data, error } = await supabase
        .from('wedding_budget_items')
        .select('*')
        .eq('id', itemId)
        .eq('budget_id', budget.id)
        .single();

      if (error) {
        if (error.code === 'PGRST116') {
          return null;
        }
        throw error;
      }

      return data;
    } catch (error) {
      handleSupabaseError(error);
    }
  }
};

// Combined budget operations
export const budgetOperations = {
  // Get complete budget data (budget + items + summary)
  async getCompleteBudgetData(userId: string): Promise<{
    budget: Budget | null;
    items: BudgetItem[];
    summary: BudgetSummary | null;
  }> {
    try {
      const [budget, items, summary] = await Promise.all([
        budgetService.getBudget(userId),
        budgetItemService.getBudgetItems(userId),
        budgetService.getBudgetSummary(userId)
      ]);

      return { budget, items, summary };
    } catch (error) {
      handleSupabaseError(error);
    }
  },

  // Get ceremony-specific budget data
  async getCeremonyBudgetData(userId: string, ceremonyType: CeremonyType): Promise<{
    budget: BudgetWithCeremony | null;
    items: BudgetItemWithCeremony[];
    summary: BudgetSummary | null;
    template: ReturnType<typeof getCeremonyBudgetTemplate>;
  }> {
    try {
      const [budget, items, summary] = await Promise.all([
        budgetService.getBudget(userId),
        budgetItemService.getBudgetItems(userId),
        budgetService.getBudgetSummary(userId)
      ]);

      const template = getCeremonyBudgetTemplate(ceremonyType);

      return {
        budget: budget as BudgetWithCeremony,
        items: items as BudgetItemWithCeremony[],
        summary,
        template
      };
    } catch (error) {
      handleSupabaseError(error);
    }
  },

  // Initialize budget with default categories
  async initializeBudgetWithDefaults(userId: string, totalBudget: number): Promise<{
    budget: Budget;
    items: BudgetItem[];
  }> {
    try {
      // Create budget
      const budget = await budgetService.upsertBudget(userId, {
        total_budget: totalBudget,
        currency: 'UGX'
      });

      // Create default budget items (wedding categories for backward compatibility)
      const defaultCategories = [
        'Venue & Catering',
        'Photography & Videography',
        'Attire & Accessories',
        'Decoration & Flowers',
        'Music & Entertainment',
        'Transportation',
        'Accommodation',
        'Wedding Rings',
        'Stationery & Invitations',
        'Beauty & Grooming',
        'Wedding Cake',
        'Gifts & Favors',
        'Legal & Documentation',
        'Contingency',
        'Other'
      ];

      const items = await Promise.all(
        defaultCategories.map(category =>
          budgetItemService.createBudgetItem(userId, {
            budget_id: budget.id,
            category,
            estimated_cost: 0,
            actual_cost: null,
            notes: null,
            is_completed: false
          })
        )
      );

      return { budget, items };
    } catch (error) {
      handleSupabaseError(error);
    }
  },

  // Initialize budget with ceremony-specific categories
  async initializeCeremonyBudget(userId: string, totalBudget: number, ceremonyType: CeremonyType): Promise<{
    budget: Budget;
    items: BudgetItem[];
  }> {
    try {
      // Use the database function to initialize ceremony budget
      const { error } = await supabase
        .rpc('initialize_ceremony_budget', {
          p_user_id: userId,
          p_ceremony_type: ceremonyType,
          p_total_budget: totalBudget,
          p_currency: 'UGX'
        });

      if (error) throw error;

      // Get the created budget and items
      const budget = await budgetService.getBudget(userId);
      if (!budget) {
        throw new Error('Failed to create budget');
      }

      const items = await budgetItemService.getBudgetItemsByBudgetId(budget.id);

      return { budget, items };
    } catch (error) {
      handleSupabaseError(error);
    }
  }
};

