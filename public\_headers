# Security Headers for Production Deployment
# This file is used by Netlify and other hosting providers

/*
  # Security Headers
  X-Frame-Options: DENY
  X-Content-Type-Options: nosniff
  X-XSS-Protection: 1; mode=block
  Referrer-Policy: strict-origin-when-cross-origin
  
  # Content Security Policy
  Content-Security-Policy: default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval' https://www.googletagmanager.com https://www.google-analytics.com; style-src 'self' 'unsafe-inline' https://fonts.googleapis.com; font-src 'self' https://fonts.gstatic.com; img-src 'self' data: https: blob:; connect-src 'self' https://*.supabase.co wss://*.supabase.co https://www.google-analytics.com; frame-ancestors 'none'; base-uri 'self'; form-action 'self';
  
  # Permissions Policy
  Permissions-Policy: camera=(), microphone=(), geolocation=(), payment=(), usb=(), magnetometer=(), gyroscope=(), accelerometer=()
  
  # Cache Control for HTML
  Cache-Control: no-cache, no-store, must-revalidate
  
  # HSTS (HTTP Strict Transport Security)
  Strict-Transport-Security: max-age=31536000; includeSubDomains; preload

# Static Assets Caching
/assets/*
  Cache-Control: public, max-age=31536000, immutable

# Images Caching
/images/*
  Cache-Control: public, max-age=86400

# Fonts Caching
*.woff2
  Cache-Control: public, max-age=31536000, immutable

# CSS and JS Caching
*.css
  Cache-Control: public, max-age=31536000, immutable

*.js
  Cache-Control: public, max-age=31536000, immutable

# Manifest and Service Worker
/site.webmanifest
  Cache-Control: public, max-age=86400

/sw.js
  Cache-Control: no-cache, no-store, must-revalidate

# API Routes (if any)
/api/*
  Cache-Control: no-cache, no-store, must-revalidate
