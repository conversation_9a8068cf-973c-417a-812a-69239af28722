# 🧪 Testing Couple's Image Feature

## Quick Test Guide

### **Prerequisites**
- Application running at `http://localhost:8081/`
- User account with profile access
- Test images ready (JPEG, PNG, WebP under 5MB)

---

## 📋 **Test Scenarios**

### **Scenario 1: Profile Setup with Image Upload**
**Objective**: Test image upload during initial profile setup

**Steps**:
1. Create new user account or use incomplete profile
2. Navigate to Profile Setup page
3. Fill in required wedding details
4. Scroll to "Coup<PERSON>'s Photo" section
5. Test image upload:
   - **Drag & drop** an image file
   - **Click to browse** and select image
   - Try different formats (JPEG, PNG, WebP)
6. Complete profile setup
7. Navigate to pledge card to verify image display

**Expected Results**:
- ✅ Upload interface appears with drag & drop area
- ✅ Image uploads successfully with progress indication
- ✅ Image preview displays immediately after upload
- ✅ Profile saves with image URL
- ✅ Image appears on pledge card in circular format

### **Scenario 2: Profile Management - Add/Replace Image**
**Objective**: Test image management in existing profile

**Steps**:
1. Navigate to Profile page
2. Scroll to "<PERSON><PERSON><PERSON>'s Photo" field
3. Upload an image if none exists
4. Hover over existing image to reveal controls
5. Click "Change" to replace image
6. Upload different image
7. Verify new image replaces old one
8. Check pledge card reflects new image

**Expected Results**:
- ✅ Image upload field appears in profile form
- ✅ Hover controls (Change/Remove) work correctly
- ✅ Old image is deleted when replaced
- ✅ New image displays immediately
- ✅ Pledge card updates with new image

### **Scenario 3: Image Removal**
**Objective**: Test image deletion functionality

**Steps**:
1. Have profile with uploaded image
2. Go to Profile page
3. Hover over couple's image
4. Click "Remove" button
5. Confirm removal
6. Save profile changes
7. Check pledge card reverts to heart icon

**Expected Results**:
- ✅ Remove button appears on hover
- ✅ Image is deleted from storage
- ✅ Profile field clears
- ✅ Pledge card shows heart icon fallback
- ✅ No broken image links

### **Scenario 4: File Validation Testing**
**Objective**: Test upload restrictions and validation

**Steps**:
1. Try uploading unsupported formats (GIF, BMP, PDF)
2. Try uploading file larger than 5MB
3. Try uploading very small image (< 1KB)
4. Try uploading corrupted image file
5. Test with special characters in filename

**Expected Results**:
- ✅ Unsupported formats rejected with clear error
- ✅ Large files rejected with size limit message
- ✅ Small valid images upload successfully
- ✅ Corrupted files handled gracefully
- ✅ Special characters in filenames handled properly

### **Scenario 5: Mobile Responsiveness**
**Objective**: Test image feature on mobile devices

**Steps**:
1. Access application on mobile device
2. Navigate to Profile Setup or Profile page
3. Test image upload on mobile
4. Test drag & drop (if supported)
5. View pledge card on mobile
6. Test image controls on touch interface

**Expected Results**:
- ✅ Upload interface works on mobile
- ✅ File picker opens correctly
- ✅ Images display properly on small screens
- ✅ Touch controls work for Change/Remove
- ✅ Pledge card image scales appropriately

### **Scenario 6: Pledge Card Display**
**Objective**: Test image display on public pledge cards

**Steps**:
1. Create profile with couple's image
2. Make pledge card public
3. Share pledge card URL
4. View pledge card in different browsers
5. Test on different screen sizes
6. Verify image loads quickly

**Expected Results**:
- ✅ Image displays in circular format
- ✅ Proper sizing (128px mobile, 160px desktop)
- ✅ Border and shadow effects applied
- ✅ Alt text present for accessibility
- ✅ Fast loading from CDN
- ✅ Graceful fallback if image fails to load

---

## 🔧 **Technical Testing**

### **Storage Testing**
```bash
# Check Supabase Storage bucket
# Verify files are organized by user ID
# Confirm public access works
# Test file cleanup on replacement
```

### **Database Testing**
```sql
-- Verify couple_image column exists
SELECT couple_image FROM profiles WHERE id = 'user-id';

-- Check for proper URL format
SELECT couple_image FROM profiles WHERE couple_image IS NOT NULL;
```

### **Performance Testing**
- **Upload speed**: Test with different file sizes
- **Display speed**: Measure image load times
- **Storage efficiency**: Check file organization
- **CDN performance**: Test global access speeds

---

## 🐛 **Edge Cases to Test**

### **Network Issues**
- [ ] Upload interruption (disconnect during upload)
- [ ] Slow network conditions
- [ ] Upload retry functionality
- [ ] Offline behavior

### **Browser Compatibility**
- [ ] Chrome/Chromium
- [ ] Firefox
- [ ] Safari
- [ ] Edge
- [ ] Mobile browsers

### **File Edge Cases**
- [ ] Maximum file size (exactly 5MB)
- [ ] Minimum file size (1 byte)
- [ ] Very wide/tall aspect ratios
- [ ] Images with EXIF data
- [ ] Progressive JPEG files
- [ ] Animated WebP (should work as static)

### **User Experience Edge Cases**
- [ ] Multiple rapid uploads
- [ ] Upload while form is submitting
- [ ] Browser refresh during upload
- [ ] Multiple browser tabs open
- [ ] Concurrent image operations

---

## ✅ **Success Criteria**

### **Functional Requirements**
- [ ] Image upload works in Profile Setup
- [ ] Image upload works in Profile Management
- [ ] Image replacement works correctly
- [ ] Image removal works correctly
- [ ] File validation prevents invalid uploads
- [ ] Images display correctly on pledge cards

### **Technical Requirements**
- [ ] Files stored securely in Supabase Storage
- [ ] Proper user isolation (users can't access others' images)
- [ ] Public access works for pledge card display
- [ ] Old images cleaned up when replaced
- [ ] Database updates correctly with image URLs

### **User Experience Requirements**
- [ ] Intuitive drag & drop interface
- [ ] Clear upload progress indication
- [ ] Helpful error messages
- [ ] Responsive design works on all devices
- [ ] Fast image loading and display
- [ ] Accessible for screen readers

### **Performance Requirements**
- [ ] Upload completes within 10 seconds for 5MB file
- [ ] Images load within 2 seconds on pledge cards
- [ ] No memory leaks during multiple uploads
- [ ] Efficient storage usage

---

## 🚨 **Common Issues & Solutions**

### **Upload Fails**
- Check file format and size
- Verify network connection
- Check browser console for errors
- Ensure Supabase Storage is configured

### **Image Doesn't Display**
- Verify image URL is valid
- Check public access permissions
- Test direct URL access
- Check for CORS issues

### **Slow Upload**
- Check file size (compress if needed)
- Test network speed
- Verify CDN configuration
- Check for browser extensions blocking

### **Mobile Issues**
- Test file picker on different mobile browsers
- Verify touch controls work
- Check responsive image sizing
- Test on various screen sizes

---

## 📊 **Test Results Template**

### **Test Summary**
- **Date**: ___________
- **Tester**: ___________
- **Environment**: ___________
- **Browser**: ___________

### **Results**
- **Profile Setup Upload**: ✅ / ❌
- **Profile Management**: ✅ / ❌
- **Image Replacement**: ✅ / ❌
- **Image Removal**: ✅ / ❌
- **File Validation**: ✅ / ❌
- **Mobile Responsiveness**: ✅ / ❌
- **Pledge Card Display**: ✅ / ❌

### **Issues Found**
1. ___________
2. ___________
3. ___________

### **Overall Assessment**
- **Ready for Production**: ✅ / ❌
- **Confidence Level**: ___/10
- **Recommendations**: ___________

---

**The couple's image feature significantly enhances the personalization of pledge cards. Thorough testing ensures a smooth user experience and reliable functionality.** 📸✨
