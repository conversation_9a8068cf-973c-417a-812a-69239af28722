import type { Database } from './database';

// Ceremony Types
export type CeremonyType = 'wedding' | 'kukyala' | 'nikah' | 'kuhingira';

// Domain Types
export type WeddingProfile = Database['public']['Tables']['profiles']['Row'];
export type WeddingProfileInsert = Database['public']['Tables']['profiles']['Insert'];
export type WeddingProfileUpdate = Database['public']['Tables']['profiles']['Update'];

export type Pledge = Database['public']['Tables']['pledges']['Row'];
export type PledgeInsert = Database['public']['Tables']['pledges']['Insert'];
export type PledgeUpdate = Database['public']['Tables']['pledges']['Update'];

export type PaymentStatus = NonNullable<Database['public']['Tables']['pledges']['Row']['payment_status']>;

// Budget Types
export type Budget = Database['public']['Tables']['wedding_budgets']['Row'];
export type BudgetInsert = Database['public']['Tables']['wedding_budgets']['Insert'];
export type BudgetUpdate = Database['public']['Tables']['wedding_budgets']['Update'];

export type BudgetItem = Database['public']['Tables']['wedding_budget_items']['Row'];
export type BudgetItemInsert = Database['public']['Tables']['wedding_budget_items']['Insert'];
export type BudgetItemUpdate = Database['public']['Tables']['wedding_budget_items']['Update'];

export type BudgetSummary = Database['public']['Functions']['get_budget_summary']['Returns'][0];

// Ceremony Budget Types
export interface CeremonyBudgetCategory {
  name: string;
  description?: string;
  isEssential: boolean;
  typicalPercentage?: number;
  culturalNotes?: string;
}

export interface CeremonyBudgetTemplate {
  ceremonyType: CeremonyType;
  displayName: string;
  description: string;
  categories: CeremonyBudgetCategory[];
  culturalNotes: string;
  typicalRange: { min: number; max: number };
  currency: string;
}

export interface BudgetWithCeremony extends Budget {
  ceremony_type?: CeremonyType;
}

export interface BudgetItemWithCeremony extends BudgetItem {
  ceremony_type?: CeremonyType;
}

// Custom Error Types
export class AppError extends Error {
  constructor(
    message: string,
    public code: string,
    public status: number = 400
  ) {
    super(message);
    this.name = 'AppError';
  }
}

export class AuthError extends AppError {
  constructor(message: string) {
    super(message, 'AUTH_ERROR', 401);
    this.name = 'AuthError';
  }
}

export class ValidationError extends AppError {
  constructor(message: string) {
    super(message, 'VALIDATION_ERROR', 400);
    this.name = 'ValidationError';
  }
}

export class DatabaseError extends AppError {
  constructor(message: string) {
    super(message, 'DATABASE_ERROR', 500);
    this.name = 'DatabaseError';
  }
}

// Validation Types
export interface ValidationResult {
  isValid: boolean;
  errors: string[];
  normalizedValue?: string;
}

// Utility Functions
export const handleSupabaseError = (error: unknown): never => {
  console.error('Supabase operation failed:', error);
  
  if (error instanceof Error) {
    if (error.message.includes('auth')) {
      throw new AuthError(error.message);
    }
    if (error.message.includes('validation')) {
      throw new ValidationError(error.message);
    }
    throw new DatabaseError(error.message);
  }
  
  throw new DatabaseError('An unexpected error occurred');
};

// Phone number normalization function
export const normalizePhoneNumber = (phone: string): string => {
  // Remove all spaces, dashes, and parentheses
  const cleaned = phone.replace(/[\s\-()]/g, '');

  // Handle different Ugandan phone formats
  if (cleaned.startsWith('+256')) {
    return cleaned; // Already in international format
  } else if (cleaned.startsWith('256')) {
    return '+' + cleaned; // Add + prefix
  } else if (cleaned.startsWith('0')) {
    return '+256' + cleaned.substring(1); // Replace 0 with +256
  } else if (cleaned.length === 9 && /^[0-9]{9}$/.test(cleaned)) {
    return '+256' + cleaned; // Assume it's a 9-digit number without prefix
  }

  return cleaned; // Return as-is if no pattern matches
};

// Validation Functions
export const validatePhoneNumber = (phone: string): ValidationResult => {
  const errors: string[] = [];

  if (!phone.trim()) {
    return { isValid: true, errors: [] }; // Allow empty phone numbers
  }

  // Normalize the phone number first
  const normalizedPhone = normalizePhoneNumber(phone);

  // Comprehensive phone number validation for Uganda
  // Accepts: +256XXXXXXXXX format with 9 digits after +256
  // Valid Ugandan mobile prefixes: 70x, 71x, 72x, 73x, 74x, 75x, 76x, 77x, 78x, 79x (MTN)
  // 20x, 25x, 26x (Airtel), 31x, 32x, 39x (Africell), etc.
  const phoneRegex = /^\+256[0-9]{9}$/;
  const validPrefixRegex = /^\+256[2-9][0-9]{8}$/; // More restrictive - starts with 2-9 after country code

  if (!phoneRegex.test(normalizedPhone)) {
    errors.push('Please enter a valid Ugandan phone number (e.g., +256 700 123 456, 0700 123 456, or 700123456)');
  } else if (!validPrefixRegex.test(normalizedPhone)) {
    errors.push('Please enter a valid Ugandan mobile number (should start with 2-9 after +256)');
  }

  return {
    isValid: errors.length === 0,
    errors,
    normalizedValue: normalizedPhone
  };
};

export const validateAmount = (amount: number): ValidationResult => {
  const errors: string[] = [];
  
  if (amount <= 0) {
    errors.push('Amount must be greater than 0');
  }
  
  if (amount > 1000000000) { // 1 billion UGX limit
    errors.push('Amount exceeds maximum limit');
  }
  
  return {
    isValid: errors.length === 0,
    errors
  };
};

export const validateWeddingDate = (date: string): ValidationResult => {
  const errors: string[] = [];
  const weddingDate = new Date(date);
  const today = new Date();
  
  if (isNaN(weddingDate.getTime())) {
    errors.push('Invalid date format');
  } else if (weddingDate < today) {
    errors.push('Wedding date cannot be in the past');
  }
  
  return {
    isValid: errors.length === 0,
    errors
  };
};

// Type Guards
export const isAppError = (error: unknown): error is AppError => {
  return error instanceof AppError;
};

export const isAuthError = (error: unknown): error is AuthError => {
  return error instanceof AuthError;
};

export const isValidationError = (error: unknown): error is ValidationError => {
  return error instanceof ValidationError;
};

export const isDatabaseError = (error: unknown): error is DatabaseError => {
  return error instanceof DatabaseError;
}; 