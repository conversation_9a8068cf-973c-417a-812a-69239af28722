
export type Json =
  | string
  | number
  | boolean
  | null
  | { [key: string]: Json | undefined }
  | Json[]

export type Database = {
  public: {
    Tables: {
      pledges: {
        Row: {
          amount_paid: number
          amount_pledged: number
          created_at: string
          guest_name: string
          guest_phone: string | null
          id: string
          notes: string | null
          payment_date: string | null
          payment_status: string
          pledge_date: string
          updated_at: string
          user_id: string
        }
        Insert: {
          amount_paid?: number
          amount_pledged?: number
          created_at?: string
          guest_name: string
          guest_phone?: string | null
          id?: string
          notes?: string | null
          payment_date?: string | null
          payment_status?: string
          pledge_date?: string
          updated_at?: string
          user_id: string
        }
        Update: {
          amount_paid?: number
          amount_pledged?: number
          created_at?: string
          guest_name?: string
          guest_phone?: string | null
          id?: string
          notes?: string | null
          payment_date?: string | null
          payment_status?: string
          pledge_date?: string
          updated_at?: string
          user_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "pledges_user_id_fkey"
            columns: ["user_id"]
            isOneToOne: false
            referencedRelation: "profiles"
            referencedColumns: ["id"]
          },
        ]
      }
      profiles: {
        Row: {
          bride_name: string
          created_at: string
          email: string
          groom_name: string
          id: string
          is_public: boolean | null
          treasurer_name: string
          treasurer_phone: string | null
          updated_at: string
          venue: string | null
          wedding_date: string
          theme: string | null
          special_message: string | null
          couple_image: string | null
          ceremony_type: string | null
          ceremony_date: string | null
          is_admin: boolean | null
          is_suspended: boolean | null
        }
        Insert: {
          bride_name: string
          created_at?: string
          email: string
          groom_name: string
          id: string
          is_public?: boolean | null
          treasurer_name: string
          treasurer_phone?: string | null
          updated_at?: string
          venue?: string | null
          wedding_date: string
          theme?: string | null
          special_message?: string | null
          couple_image?: string | null
          ceremony_type?: string | null
          ceremony_date?: string | null
          is_admin?: boolean | null
          is_suspended?: boolean | null
        }
        Update: {
          bride_name?: string
          created_at?: string
          email?: string
          groom_name?: string
          id?: string
          is_public?: boolean | null
          treasurer_name?: string
          treasurer_phone?: string | null
          updated_at?: string
          venue?: string | null
          wedding_date?: string
          theme?: string | null
          special_message?: string | null
          couple_image?: string | null
          ceremony_type?: string | null
          ceremony_date?: string | null
          is_admin?: boolean | null
          is_suspended?: boolean | null
        }
        Relationships: []
      }
      budgets: {
        Row: {
          id: string
          user_id: string
          total_budget: number
          currency: string
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          user_id: string
          total_budget: number
          currency?: string
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          user_id?: string
          total_budget?: number
          currency?: string
          created_at?: string
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "budgets_user_id_fkey"
            columns: ["user_id"]
            isOneToOne: false
            referencedRelation: "profiles"
            referencedColumns: ["id"]
          }
        ]
      }
      budget_items: {
        Row: {
          id: string
          budget_id: string
          category: string
          estimated_cost: number
          actual_cost: number | null
          notes: string | null
          is_completed: boolean
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          budget_id: string
          category: string
          estimated_cost: number
          actual_cost?: number | null
          notes?: string | null
          is_completed?: boolean
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          budget_id?: string
          category?: string
          estimated_cost?: number
          actual_cost?: number | null
          notes?: string | null
          is_completed?: boolean
          created_at?: string
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "budget_items_budget_id_fkey"
            columns: ["budget_id"]
            isOneToOne: false
            referencedRelation: "budgets"
            referencedColumns: ["id"]
          }
        ]
      }
      wedding_budgets: {
        Row: {
          id: string
          user_id: string
          total_budget: number
          currency: string
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          user_id: string
          total_budget: number
          currency?: string
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          user_id?: string
          total_budget?: number
          currency?: string
          created_at?: string
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "wedding_budgets_user_id_fkey"
            columns: ["user_id"]
            isOneToOne: false
            referencedRelation: "profiles"
            referencedColumns: ["id"]
          }
        ]
      }
      wedding_budget_items: {
        Row: {
          id: string
          budget_id: string
          category: string
          estimated_cost: number
          actual_cost: number | null
          notes: string | null
          is_completed: boolean
          ceremony_type: string | null
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          budget_id: string
          category: string
          estimated_cost: number
          actual_cost?: number | null
          notes?: string | null
          is_completed?: boolean
          ceremony_type?: string | null
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          budget_id?: string
          category?: string
          estimated_cost?: number
          actual_cost?: number | null
          notes?: string | null
          is_completed?: boolean
          ceremony_type?: string | null
          created_at?: string
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "wedding_budget_items_budget_id_fkey"
            columns: ["budget_id"]
            isOneToOne: false
            referencedRelation: "wedding_budgets"
            referencedColumns: ["id"]
          }
        ]
      }
      ceremony_budget_templates: {
        Row: {
          id: string
          ceremony_type: string
          category: string
          typical_percentage: number | null
          is_essential: boolean | null
          cultural_notes: string | null
          display_order: number | null
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          ceremony_type: string
          category: string
          typical_percentage?: number | null
          is_essential?: boolean | null
          cultural_notes?: string | null
          display_order?: number | null
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          ceremony_type?: string
          category?: string
          typical_percentage?: number | null
          is_essential?: boolean | null
          cultural_notes?: string | null
          display_order?: number | null
          created_at?: string
          updated_at?: string
        }
        Relationships: []
      }
    }
    Views: {
      [_ in never]: never
    }
    Functions: {
      get_budget_summary: {
        Args: {
          p_user_id: string
        }
        Returns: {
          total_budget: number
          total_estimated: number
          total_actual: number
          total_spent: number
          remaining_budget: number
        }[]
      }
      get_ceremony_budget_categories: {
        Args: {
          p_ceremony_type: string
        }
        Returns: {
          category: string
          typical_percentage: number
          is_essential: boolean
          cultural_notes: string
          display_order: number
        }[]
      }
      initialize_ceremony_budget: {
        Args: {
          p_user_id: string
          p_ceremony_type: string
          p_total_budget: number
          p_currency?: string
        }
        Returns: {
          budget_id: string
          items_created: number
        }[]
      }
      get_budget_summary_with_ceremony: {
        Args: {
          p_user_id: string
        }
        Returns: {
          total_budget: number
          total_estimated: number
          total_actual: number
          total_spent: number
          remaining_budget: number
          ceremony_type: string
          currency: string
        }[]
      }
    }
    Enums: {
      [_ in never]: never
    }
    CompositeTypes: {
      [_ in never]: never
    }
  }
}

type DefaultSchema = Database[Extract<keyof Database, "public">]

export type Tables<
  DefaultSchemaTableNameOrOptions extends
    | keyof (DefaultSchema["Tables"] & DefaultSchema["Views"])
    | { schema: keyof Database },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof (Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"] &
        Database[DefaultSchemaTableNameOrOptions["schema"]]["Views"])
    : never = never,
> = DefaultSchemaTableNameOrOptions extends { schema: keyof Database }
  ? (Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"] &
      Database[DefaultSchemaTableNameOrOptions["schema"]]["Views"])[TableName] extends {
      Row: infer R
    }
    ? R
    : never
  : DefaultSchemaTableNameOrOptions extends keyof (DefaultSchema["Tables"] &
        DefaultSchema["Views"])
    ? (DefaultSchema["Tables"] &
        DefaultSchema["Views"])[DefaultSchemaTableNameOrOptions] extends {
        Row: infer R
      }
      ? R
      : never
    : never

export type TablesInsert<
  DefaultSchemaTableNameOrOptions extends
    | keyof DefaultSchema["Tables"]
    | { schema: keyof Database },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"]
    : never = never,
> = DefaultSchemaTableNameOrOptions extends { schema: keyof Database }
  ? Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"][TableName] extends {
      Insert: infer I
    }
    ? I
    : never
  : DefaultSchemaTableNameOrOptions extends keyof DefaultSchema["Tables"]
    ? DefaultSchema["Tables"][DefaultSchemaTableNameOrOptions] extends {
        Insert: infer I
      }
      ? I
      : never
    : never

export type TablesUpdate<
  DefaultSchemaTableNameOrOptions extends
    | keyof DefaultSchema["Tables"]
    | { schema: keyof Database },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"]
    : never = never,
> = DefaultSchemaTableNameOrOptions extends { schema: keyof Database }
  ? Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"][TableName] extends {
      Update: infer U
    }
    ? U
    : never
  : DefaultSchemaTableNameOrOptions extends keyof DefaultSchema["Tables"]
    ? DefaultSchema["Tables"][DefaultSchemaTableNameOrOptions] extends {
        Update: infer U
      }
      ? U
      : never
    : never

export type Enums<
  DefaultSchemaEnumNameOrOptions extends
    | keyof DefaultSchema["Enums"]
    | { schema: keyof Database },
  EnumName extends DefaultSchemaEnumNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof Database[DefaultSchemaEnumNameOrOptions["schema"]]["Enums"]
    : never = never,
> = DefaultSchemaEnumNameOrOptions extends { schema: keyof Database }
  ? Database[DefaultSchemaEnumNameOrOptions["schema"]]["Enums"][EnumName]
  : DefaultSchemaEnumNameOrOptions extends keyof DefaultSchema["Enums"]
    ? DefaultSchema["Enums"][DefaultSchemaEnumNameOrOptions]
    : never

export type CompositeTypes<
  PublicCompositeTypeNameOrOptions extends
    | keyof DefaultSchema["CompositeTypes"]
    | { schema: keyof Database },
  CompositeTypeName extends PublicCompositeTypeNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof Database[PublicCompositeTypeNameOrOptions["schema"]]["CompositeTypes"]
    : never = never,
> = PublicCompositeTypeNameOrOptions extends { schema: keyof Database }
  ? Database[PublicCompositeTypeNameOrOptions["schema"]]["CompositeTypes"][CompositeTypeName]
  : PublicCompositeTypeNameOrOptions extends keyof DefaultSchema["CompositeTypes"]
    ? DefaultSchema["CompositeTypes"][PublicCompositeTypeNameOrOptions]
    : never

export const Constants = {
  public: {
    Enums: {},
  },
} as const
