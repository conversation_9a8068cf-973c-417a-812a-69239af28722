import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Textarea } from '@/components/ui/textarea';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { 
  ArrowLeft, 
  ArrowRight, 
  DollarSign, 
  Heart, 
  Star,
  CheckCircle,
  Info
} from 'lucide-react';
import { useToast } from '@/hooks/use-toast';
import { budgetOperations } from '@/integrations/budget';
import { formatCurrency } from '@/utils/budgetUtils';
import type { CeremonyType } from '@/types/app';
import { 
  ceremonyBudgetTemplates, 
  getCeremonyDisplayName,
  getAllCeremonyTypes 
} from '@/utils/ceremonyBudgetCategories';

interface BudgetSetupWizardProps {
  userId: string;
  onComplete: () => void;
  onCancel: () => void;
  initialCeremonyType?: CeremonyType;
}

interface WizardState {
  step: number;
  ceremonyType: CeremonyType;
  totalBudget: number;
  currency: string;
  notes: string;
}

export const BudgetSetupWizard: React.FC<BudgetSetupWizardProps> = ({
  userId,
  onComplete,
  onCancel,
  initialCeremonyType = 'wedding'
}) => {
  const [state, setState] = useState<WizardState>({
    step: 1,
    ceremonyType: initialCeremonyType,
    totalBudget: 0,
    currency: 'UGX',
    notes: ''
  });
  const [loading, setLoading] = useState(false);
  const { toast } = useToast();

  const totalSteps = 4;
  const progressPercentage = (state.step / totalSteps) * 100;

  const updateState = (updates: Partial<WizardState>) => {
    setState(prev => ({ ...prev, ...updates }));
  };

  const nextStep = () => {
    if (state.step < totalSteps) {
      updateState({ step: state.step + 1 });
    }
  };

  const prevStep = () => {
    if (state.step > 1) {
      updateState({ step: state.step - 1 });
    }
  };

  const handleComplete = async () => {
    try {
      setLoading(true);
      await budgetOperations.initializeCeremonyBudget(
        userId,
        state.totalBudget,
        state.ceremonyType
      );
      
      toast({
        title: "Budget Created Successfully! 🎉",
        description: `Your ${getCeremonyDisplayName(state.ceremonyType).toLowerCase()} budget has been set up with ceremony-specific categories.`,
      });
      
      onComplete();
    } catch (error) {
      console.error('Error creating budget:', error);
      toast({
        title: "Error",
        description: "Failed to create budget. Please try again.",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  const canProceed = () => {
    switch (state.step) {
      case 1:
        return state.ceremonyType !== '';
      case 2:
        return state.totalBudget > 0;
      case 3:
        return true; // Review step, always can proceed
      case 4:
        return true; // Final step
      default:
        return false;
    }
  };

  const renderStep = () => {
    switch (state.step) {
      case 1:
        return (
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Heart className="h-5 w-5" />
                Choose Your Ceremony Type
              </CardTitle>
              <CardDescription>
                Select the type of ceremony you're planning to get customized budget categories
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {getAllCeremonyTypes().map((type) => {
                  const template = ceremonyBudgetTemplates[type];
                  const isSelected = state.ceremonyType === type;
                  
                  return (
                    <Card 
                      key={type}
                      className={`cursor-pointer transition-all ${
                        isSelected 
                          ? 'ring-2 ring-primary border-primary' 
                          : 'hover:border-primary/50'
                      }`}
                      onClick={() => updateState({ ceremonyType: type })}
                    >
                      <CardHeader className="pb-3">
                        <CardTitle className="text-lg flex items-center justify-between">
                          {template.displayName}
                          {isSelected && <CheckCircle className="h-5 w-5 text-primary" />}
                        </CardTitle>
                        <CardDescription className="text-sm">
                          {template.description}
                        </CardDescription>
                      </CardHeader>
                      <CardContent>
                        <div className="space-y-2">
                          <div className="flex justify-between text-sm">
                            <span>Typical Range:</span>
                            <span className="font-medium">
                              {formatCurrency(template.typicalRange.min)} - {formatCurrency(template.typicalRange.max)}
                            </span>
                          </div>
                          <p className="text-xs text-muted-foreground">
                            {template.culturalNotes.substring(0, 100)}...
                          </p>
                        </div>
                      </CardContent>
                    </Card>
                  );
                })}
              </div>
            </CardContent>
          </Card>
        );

      case 2: {
        const selectedTemplate = ceremonyBudgetTemplates[state.ceremonyType];
        return (
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <DollarSign className="h-5 w-5" />
                Set Your Budget
              </CardTitle>
              <CardDescription>
                Enter your total budget for the {getCeremonyDisplayName(state.ceremonyType).toLowerCase()}
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="space-y-2">
                <Label htmlFor="totalBudget">Total Budget Amount</Label>
                <div className="relative">
                  <Input
                    id="totalBudget"
                    type="number"
                    placeholder="Enter amount"
                    value={state.totalBudget || ''}
                    onChange={(e) => updateState({ totalBudget: Number(e.target.value) })}
                    className="pl-12"
                  />
                  <div className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground">
                    UGX
                  </div>
                </div>
              </div>

              <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                <div className="flex items-start gap-3">
                  <Info className="h-5 w-5 text-blue-600 mt-0.5" />
                  <div>
                    <h4 className="font-medium text-blue-900">Budget Guidance</h4>
                    <p className="text-sm text-blue-800 mt-1">
                      {selectedTemplate.culturalNotes}
                    </p>
                    <div className="mt-2">
                      <p className="text-sm font-medium text-blue-900">
                        Typical Range: {formatCurrency(selectedTemplate.typicalRange.min)} - {formatCurrency(selectedTemplate.typicalRange.max)}
                      </p>
                    </div>
                  </div>
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="currency">Currency</Label>
                <Select value={state.currency} onValueChange={(value) => updateState({ currency: value })}>
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="UGX">UGX - Ugandan Shilling</SelectItem>
                    <SelectItem value="USD">USD - US Dollar</SelectItem>
                    <SelectItem value="EUR">EUR - Euro</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label htmlFor="notes">Notes (Optional)</Label>
                <Textarea
                  id="notes"
                  placeholder="Any additional notes about your budget..."
                  value={state.notes}
                  onChange={(e) => updateState({ notes: e.target.value })}
                  rows={3}
                />
              </div>
            </CardContent>
          </Card>
        );
      }

      case 3: {
        const template = ceremonyBudgetTemplates[state.ceremonyType];
        return (
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Star className="h-5 w-5" />
                Budget Categories Preview
              </CardTitle>
              <CardDescription>
                Review the categories that will be created for your {getCeremonyDisplayName(state.ceremonyType).toLowerCase()}
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {template.categories.map((category, index) => {
                  const estimatedAmount = category.typicalPercentage 
                    ? (state.totalBudget * category.typicalPercentage) / 100
                    : 0;
                  
                  return (
                    <div key={index} className="border rounded-lg p-3">
                      <div className="flex items-start justify-between">
                        <div className="flex-1">
                          <div className="flex items-center gap-2">
                            <h4 className="font-medium">{category.name}</h4>
                            {category.isEssential && (
                              <Badge variant="secondary" className="text-xs">Essential</Badge>
                            )}
                          </div>
                          {category.description && (
                            <p className="text-sm text-muted-foreground mt-1">
                              {category.description}
                            </p>
                          )}
                        </div>
                        <div className="text-right">
                          {category.typicalPercentage && (
                            <p className="text-sm font-medium">
                              {formatCurrency(estimatedAmount)}
                            </p>
                          )}
                          {category.typicalPercentage && (
                            <p className="text-xs text-muted-foreground">
                              {category.typicalPercentage}%
                            </p>
                          )}
                        </div>
                      </div>
                    </div>
                  );
                })}
              </div>
            </CardContent>
          </Card>
        );
      }

      case 4:
        return (
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <CheckCircle className="h-5 w-5" />
                Review & Confirm
              </CardTitle>
              <CardDescription>
                Review your budget setup before creating
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="space-y-4">
                <div className="flex justify-between">
                  <span className="font-medium">Ceremony Type:</span>
                  <span>{getCeremonyDisplayName(state.ceremonyType)}</span>
                </div>
                <div className="flex justify-between">
                  <span className="font-medium">Total Budget:</span>
                  <span className="text-lg font-bold">
                    {formatCurrency(state.totalBudget, state.currency)}
                  </span>
                </div>
                <div className="flex justify-between">
                  <span className="font-medium">Categories:</span>
                  <span>{ceremonyBudgetTemplates[state.ceremonyType].categories.length} categories</span>
                </div>
                {state.notes && (
                  <div>
                    <span className="font-medium">Notes:</span>
                    <p className="text-sm text-muted-foreground mt-1">{state.notes}</p>
                  </div>
                )}
              </div>

              <div className="bg-green-50 border border-green-200 rounded-lg p-4">
                <h4 className="font-medium text-green-900">What happens next?</h4>
                <ul className="text-sm text-green-800 mt-2 space-y-1">
                  <li>• Budget categories will be created with estimated amounts</li>
                  <li>• You can adjust individual category budgets anytime</li>
                  <li>• Track actual expenses against your estimates</li>
                  <li>• Export your budget data to Excel when needed</li>
                </ul>
              </div>
            </CardContent>
          </Card>
        );

      default:
        return null;
    }
  };

  return (
    <div className="max-w-4xl mx-auto space-y-6">
      {/* Progress Header */}
      <div className="space-y-2">
        <div className="flex items-center justify-between">
          <h2 className="text-2xl font-bold">Budget Setup</h2>
          <span className="text-sm text-muted-foreground">
            Step {state.step} of {totalSteps}
          </span>
        </div>
        <Progress value={progressPercentage} className="h-2" />
      </div>

      {/* Step Content */}
      {renderStep()}

      {/* Navigation */}
      <div className="flex justify-between">
        <Button 
          variant="outline" 
          onClick={state.step === 1 ? onCancel : prevStep}
          disabled={loading}
        >
          <ArrowLeft className="h-4 w-4 mr-2" />
          {state.step === 1 ? 'Cancel' : 'Previous'}
        </Button>

        <Button 
          onClick={state.step === totalSteps ? handleComplete : nextStep}
          disabled={!canProceed() || loading}
        >
          {state.step === totalSteps ? (
            loading ? 'Creating...' : 'Create Budget'
          ) : (
            <>
              Next
              <ArrowRight className="h-4 w-4 ml-2" />
            </>
          )}
        </Button>
      </div>
    </div>
  );
};

export default BudgetSetupWizard;
