import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { ArrowRight, Heart, Users, Calendar, Shield, Diamond, Gift, Sparkles } from "lucide-react";
import { useNavigate } from "react-router-dom";
import { useSEO } from "@/utils/seo";
import SocialShare from "@/components/SocialShare";
import { HomePageStructuredData, LocalBusinessStructuredData } from "@/components/StructuredData";
import { useAuthModal } from "@/contexts/AuthModalContext";
import { useAuth } from "@/contexts/AuthContext";

const Index = () => {
  const navigate = useNavigate();
  const { openAuthModal } = useAuthModal();
  const { user } = useAuth();

  // SEO optimization for home page
  useSEO('home');

  const handleMainAction = () => {
    if (user) {
      navigate('/dashboard');
    } else {
      openAuthModal('signup');
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-rose-50 via-pink-50 to-amber-50 relative overflow-hidden">
      {/* Structured Data for SEO */}
      <HomePageStructuredData />
      <LocalBusinessStructuredData />
      {/* Decorative Elements */}
      <div className="absolute top-0 left-0 w-full h-full opacity-10">
        <div className="absolute top-20 left-10 w-32 h-32 bg-pink-300 rounded-full mix-blend-multiply filter blur-xl"></div>
        <div className="absolute bottom-20 right-10 w-40 h-40 bg-purple-300 rounded-full mix-blend-multiply filter blur-xl"></div>
        <div className="absolute top-1/2 left-1/4 w-24 h-24 bg-amber-200 rounded-full mix-blend-multiply filter blur-xl"></div>
      </div>

      {/* Floating Hearts */}
      <div className="absolute top-1/4 right-16 animate-float">
        <Heart className="h-8 w-8 text-pink-400 fill-pink-200" />
      </div>
      <div className="absolute top-1/3 left-20 animate-float-delay">
        <Heart className="h-6 w-6 text-rose-400 fill-rose-200" />
      </div>

      {/* Main Content */}
      <div className="container mx-auto px-4 py-8 md:py-12 relative z-10">
        {/* Hero Section */}
        <div className="text-center mb-12 md:mb-20">
          <div className="flex flex-col items-center mb-6 md:mb-8">
            <div className="relative mb-4 md:mb-6">
              <Diamond className="h-12 w-12 md:h-16 md:w-16 text-rose-500 absolute -top-3 -left-3 md:-top-4 md:-left-4 animate-spin-slow" />
              <Heart className="h-16 w-16 md:h-20 md:w-20 text-pink-500 fill-pink-300 animate-pulse" />
            </div>
            <h1 className="text-4xl sm:text-5xl md:text-7xl font-bold bg-gradient-to-r from-rose-600 via-pink-600 to-amber-500 bg-clip-text text-transparent font-serif tracking-tight px-4">
              PledgeForLove
            </h1>
            <p className="text-lg sm:text-xl md:text-2xl text-rose-900/80 mt-3 md:mt-4 mb-6 md:mb-8 max-w-3xl mx-auto font-medium px-4">
              Transform your wedding contributions with beautiful digital pledge cards. Simple and elegant.
            </p>
          </div>
          
          <div className="flex flex-col sm:flex-row gap-4 justify-center px-4">
            <Button 
              size="lg" 
              className="bg-gradient-to-r from-rose-500 to-pink-600 hover:from-rose-600 hover:to-pink-700 text-white px-6 md:px-8 py-5 md:py-6 text-base md:text-lg shadow-lg hover:shadow-xl transition-all duration-300 group w-full sm:w-auto"
              onClick={handleMainAction}
            >
              <span className="group-hover:scale-105 transition-transform">
                {user ? 'Dashboard' : 'Create Pledge Card'}
              </span>
              <ArrowRight className="ml-2 h-4 w-4 md:h-5 md:w-5 group-hover:translate-x-1 transition-transform" />
            </Button>
          </div>
        </div>

        {/* Features Grid */}
        <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8 mb-20">
          {[
            { 
              icon: <Heart className="h-8 w-8 text-white" />, 
              bg: "from-rose-400 to-pink-500",
              title: "Romantic Designs", 
              desc: "Elegant pledge cards that reflect your love story"
            },
            { 
              icon: <Gift className="h-8 w-8 text-white" />, 
              bg: "from-amber-400 to-orange-500",
              title: "Thoughtful Giving", 
              desc: "Make contributing joyful for your guests"
            },
            { 
              icon: <Users className="h-8 w-8 text-white" />, 
              bg: "from-purple-400 to-indigo-500",
              title: "Easy Sharing", 
              desc: "Share with family worldwide in one click"
            },
            { 
              icon: <Sparkles className="h-8 w-8 text-white" />, 
              bg: "from-blue-400 to-teal-500",
              title: "Magical Experience", 
              desc: "Create unforgettable memories together"
            }
          ].map((feature, index) => (
            <Card 
              key={index}
              className="bg-white/90 backdrop-blur-sm border border-pink-100 shadow-md hover:shadow-lg transition-all duration-300 hover:-translate-y-1"
            >
              <CardContent className="p-6 text-center">
                <div className={`w-16 h-16 bg-gradient-to-br ${feature.bg} rounded-full flex items-center justify-center mx-auto mb-4`}>
                  {feature.icon}
                </div>
                <h3 className="text-xl font-bold text-rose-900 mb-2">{feature.title}</h3>
                <p className="text-rose-800/80">{feature.desc}</p>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Testimonial Section */}
        <div className="bg-gradient-to-r from-rose-500/10 to-pink-500/10 rounded-3xl p-8 md:p-12 mb-20 border border-pink-200/50">
          <div className="max-w-4xl mx-auto text-center">
            <Heart className="h-10 w-10 text-rose-500 fill-rose-200 mx-auto mb-6" />
            <blockquote className="text-2xl md:text-3xl font-serif italic text-rose-900 mb-6">
              "PledgeForLove transformed how our families contributed to our wedding. It was beautiful, 
              organized, and made everyone feel connected to our special day."
            </blockquote>
            <div className="text-pink-600 font-medium">— Sarah & Michael, June 2023</div>
          </div>
        </div>

        {/* How It Works */}
        <div className="text-center mb-20">
          <h2 className="text-3xl md:text-4xl font-bold text-rose-900 mb-12 font-serif">
            Three Simple Steps
          </h2>
          <div className="grid md:grid-cols-3 gap-8 max-w-6xl mx-auto">
            {[
              {
                emoji: "💍",
                title: "Create Your Card",
                desc: "Design a beautiful digital pledge card in minutes"
              },
              {
                emoji: "📲",
                title: "Share With Loved Ones",
                desc: "Send via WhatsApp, email, or social media"
              },
              {
                emoji: "💰",
                title: "Receive Contributions",
                desc: "Track pledges and payments in real-time"
              }
            ].map((step, index) => (
              <div key={index} className="bg-white/80 p-8 rounded-2xl border border-pink-100 shadow-sm hover:shadow-md transition-shadow">
                <div className="text-5xl mb-4">{step.emoji}</div>
                <div className="relative mb-6">
                  <div className="absolute -top-8 left-1/2 transform -translate-x-1/2 bg-pink-500 text-white rounded-full h-8 w-8 flex items-center justify-center">
                    {index + 1}
                  </div>
                  <h3 className="text-xl font-bold text-rose-800 mt-2">{step.title}</h3>
                </div>
                <p className="text-rose-700/90">{step.desc}</p>
              </div>
            ))}
          </div>
        </div>

        {/* Social Sharing Section */}
        <div className="py-16 bg-gradient-to-r from-rose-50/50 to-pink-50/50 rounded-3xl border border-pink-100/50 mb-8">
          <div className="max-w-4xl mx-auto px-6 text-center">
            <div className="flex justify-center mb-6">
              <div className="bg-pink-100 p-4 rounded-full">
                <Heart className="h-8 w-8 text-pink-600" />
              </div>
            </div>
            <h2 className="text-3xl font-bold text-rose-800 mb-4">
              Share PledgeForLove with Others
            </h2>
            <p className="text-rose-700/90 mb-8 max-w-2xl mx-auto">
              Help other couples discover the joy of digital wedding contributions.
              Share our platform with friends and family planning their special day.
            </p>
            <div className="bg-white/80 p-6 rounded-2xl border border-pink-100 shadow-sm">
              <SocialShare
                url="https://p4love.com"
                title="PledgeForLove - Transform Your Wedding Contributions"
                description="Discover the modern way to handle wedding contributions with beautiful digital pledge cards!"
                hashtags={["PledgeForLove", "DigitalWedding", "WeddingPlanning"]}
                className="justify-center"
              />
            </div>
          </div>
        </div>
      </div>

      {/* Floating animated hearts */}
      <style>{`
        @keyframes float {
          0%, 100% { transform: translateY(0); }
          50% { transform: translateY(-15px); }
        }
        @keyframes float-delay {
          0%, 100% { transform: translateY(-5px); }
          50% { transform: translateY(10px); }
        }
        .animate-float { animation: float 6s ease-in-out infinite; }
        .animate-float-delay { animation: float-delay 7s ease-in-out infinite; }
        .animate-spin-slow { animation: spin 20s linear infinite; }
        @keyframes spin { from { transform: rotate(0deg); } to { transform: rotate(360deg); } }
      `}</style>
    </div>
  );
};

export default Index;